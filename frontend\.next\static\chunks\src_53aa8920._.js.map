{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n// Utility function to format job descriptions with proper line breaks and formatting\r\nexport function formatJobDescription(description) {\r\n  if (!description) return \"No description provided.\";\r\n  \r\n  return description\r\n    .replace(/\\n/g, '<br />') // Convert newlines to HTML breaks\r\n    .replace(/•/g, '•') // Ensure bullet points are preserved\r\n    .replace(/\\*\\s/g, '• ') // Convert asterisk bullets to bullet symbols\r\n    .replace(/-\\s/g, '• ') // Convert dash bullets to bullet symbols\r\n    .trim();\r\n}\r\n\r\n// Component for rendering formatted job descriptions\r\nexport function FormattedJobDescription({ description, className = \"\" }) {\r\n  const formattedDescription = formatJobDescription(description);\r\n  \r\n  return (\r\n    <div \r\n      className={`text-gray-700 leading-relaxed ${className}`}\r\n      dangerouslySetInnerHTML={{ __html: formattedDescription }}\r\n    />\r\n  );\r\n}\r\n\r\n// Utility function to standardize field names in API responses\r\nexport function standardizeFieldNames(data, entityType) {\r\n  if (!data) return data;\r\n  \r\n  // Define standard field name mappings for different entity types\r\n  const fieldMappings = {\r\n    company: {\r\n      totalActiveJobs: 'total_active_jobs',\r\n      totalApplicants: 'total_applicants',\r\n      totalHired: 'total_hired',\r\n      awaitedApproval: 'pending_approval',\r\n      companyName: 'name',\r\n      companySize: 'size',\r\n      companyIndustry: 'industry',\r\n      companyLocation: 'location'\r\n    },\r\n    student: {\r\n      firstName: 'first_name',\r\n      lastName: 'last_name',\r\n      contactEmail: 'email',\r\n      studentId: 'student_id',\r\n      joiningYear: 'joining_year',\r\n      passoutYear: 'passout_year',\r\n      tenthCertificate: 'tenth_certificate',\r\n      twelfthCertificate: 'twelfth_certificate'\r\n    },\r\n    job: {\r\n      jobTitle: 'title',\r\n      jobType: 'job_type',\r\n      jobLocation: 'location',\r\n      salaryMin: 'salary_min',\r\n      salaryMax: 'salary_max',\r\n      requiredSkills: 'required_skills',\r\n      applicationDeadline: 'application_deadline',\r\n      isActive: 'is_active',\r\n      companyName: 'company_name'\r\n    }\r\n  };\r\n  \r\n  // Return original data if entity type is not supported\r\n  if (!fieldMappings[entityType]) return data;\r\n  \r\n  // If it's an array, standardize each item\r\n  if (Array.isArray(data)) {\r\n    return data.map(item => standardizeFieldNames(item, entityType));\r\n  }\r\n  \r\n  // For single objects, standardize fields\r\n  const standardized = { ...data };\r\n  const mapping = fieldMappings[entityType];\r\n  \r\n  // Apply field name standardization\r\n  Object.keys(mapping).forEach(nonStandardField => {\r\n    const standardField = mapping[nonStandardField];\r\n    \r\n    // If non-standard field exists in the data, copy it to the standard field\r\n    if (standardized[nonStandardField] !== undefined && standardized[standardField] === undefined) {\r\n      standardized[standardField] = standardized[nonStandardField];\r\n      delete standardized[nonStandardField];\r\n    }\r\n  });\r\n  \r\n  return standardized;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,qBAAqB,WAAW;IAC9C,IAAI,CAAC,aAAa,OAAO;IAEzB,OAAO,YACJ,OAAO,CAAC,OAAO,UAAU,kCAAkC;KAC3D,OAAO,CAAC,MAAM,KAAK,qCAAqC;KACxD,OAAO,CAAC,SAAS,MAAM,6CAA6C;KACpE,OAAO,CAAC,QAAQ,MAAM,yCAAyC;KAC/D,IAAI;AACT;AAGO,SAAS,wBAAwB,EAAE,WAAW,EAAE,YAAY,EAAE,EAAE;IACrE,MAAM,uBAAuB,qBAAqB;IAElD,qBACE,6LAAC;QACC,WAAW,CAAC,8BAA8B,EAAE,WAAW;QACvD,yBAAyB;YAAE,QAAQ;QAAqB;;;;;;AAG9D;KATgB;AAYT,SAAS,sBAAsB,IAAI,EAAE,UAAU;IACpD,IAAI,CAAC,MAAM,OAAO;IAElB,iEAAiE;IACjE,MAAM,gBAAgB;QACpB,SAAS;YACP,iBAAiB;YACjB,iBAAiB;YACjB,YAAY;YACZ,iBAAiB;YACjB,aAAa;YACb,aAAa;YACb,iBAAiB;YACjB,iBAAiB;QACnB;QACA,SAAS;YACP,WAAW;YACX,UAAU;YACV,cAAc;YACd,WAAW;YACX,aAAa;YACb,aAAa;YACb,kBAAkB;YAClB,oBAAoB;QACtB;QACA,KAAK;YACH,UAAU;YACV,SAAS;YACT,aAAa;YACb,WAAW;YACX,WAAW;YACX,gBAAgB;YAChB,qBAAqB;YACrB,UAAU;YACV,aAAa;QACf;IACF;IAEA,uDAAuD;IACvD,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,OAAO;IAEvC,0CAA0C;IAC1C,IAAI,MAAM,OAAO,CAAC,OAAO;QACvB,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,sBAAsB,MAAM;IACtD;IAEA,yCAAyC;IACzC,MAAM,eAAe;QAAE,GAAG,IAAI;IAAC;IAC/B,MAAM,UAAU,aAAa,CAAC,WAAW;IAEzC,mCAAmC;IACnC,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,CAAA;QAC3B,MAAM,gBAAgB,OAAO,CAAC,iBAAiB;QAE/C,0EAA0E;QAC1E,IAAI,YAAY,CAAC,iBAAiB,KAAK,aAAa,YAAY,CAAC,cAAc,KAAK,WAAW;YAC7F,YAAY,CAAC,cAAc,GAAG,YAAY,CAAC,iBAAiB;YAC5D,OAAO,YAAY,CAAC,iBAAiB;QACvC;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/errorHandler.js"], "sourcesContent": ["import { useNotification } from '../contexts/NotificationContext';\r\n\r\n// Error codes and their corresponding handlers\r\nexport const ERROR_PATTERNS = {\r\n  AUTHENTICATION: {\r\n    codes: [401],\r\n    keywords: ['unauthorized', 'authentication', 'token', 'login'],\r\n    handler: 'showAuthError'\r\n  },\r\n  SESSION_EXPIRED: {\r\n    codes: [401],\r\n    keywords: ['expired', 'invalid token', 'token expired'],\r\n    handler: 'showSessionExpiredModal'\r\n  },\r\n  PERMISSION_DENIED: {\r\n    codes: [403],\r\n    keywords: ['permission', 'forbidden', 'access denied'],\r\n    handler: 'showAuthError'\r\n  },\r\n  VALIDATION: {\r\n    codes: [400, 422],\r\n    keywords: ['validation', 'invalid', 'required'],\r\n    handler: 'showValidationError'\r\n  },\r\n  RESUME_REQUIRED: {\r\n    fields: ['resume'],\r\n    keywords: ['resume', 'must be uploaded', 'present in the student profile'],\r\n    handler: 'showMissingResumeModal'\r\n  },\r\n  PROFILE_INCOMPLETE: {\r\n    keywords: ['profile incomplete', 'missing profile', 'update profile'],\r\n    handler: 'showProfileIncompleteModal'\r\n  },\r\n  FILE_UPLOAD: {\r\n    keywords: ['file', 'upload', 'size', 'format', 'extension'],\r\n    handler: 'showFileUploadError'\r\n  },\r\n  NETWORK_ERROR: {\r\n    codes: ['NETWORK_ERROR', 'ECONNREFUSED', 'ERR_NETWORK'],\r\n    keywords: ['network', 'connection', 'timeout'],\r\n    handler: 'showNetworkError'\r\n  },\r\n  MAINTENANCE: {\r\n    codes: [503, 502],\r\n    keywords: ['maintenance', 'service unavailable', 'temporarily unavailable'],\r\n    handler: 'showMaintenanceModal'\r\n  }\r\n};\r\n\r\n// Smart error detection and handling\r\nexport const detectAndHandleError = (error, context = '', notificationHandlers) => {\r\n  const errorData = error?.response?.data || {};\r\n  const errorMessage = (errorData.detail || errorData.message || error.message || '').toLowerCase();\r\n  const statusCode = error?.response?.status;\r\n\r\n  // Check for specific error patterns\r\n  for (const [pattern, config] of Object.entries(ERROR_PATTERNS)) {\r\n    // Check status codes\r\n    if (config.codes && config.codes.includes(statusCode)) {\r\n      // Additional keyword check for more precision\r\n      if (config.keywords && !config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n        continue;\r\n      }\r\n      \r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check for field-specific errors (like resume)\r\n    if (config.fields && config.fields.some(field => errorData[field])) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check keywords in error message\r\n    if (config.keywords && config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n  }\r\n\r\n  // Fallback to generic error handling\r\n  return handleGenericError(error, context, notificationHandlers);\r\n};\r\n\r\nconst handleSpecificError = (pattern, error, context, notificationHandlers) => {\r\n  const config = ERROR_PATTERNS[pattern];\r\n  const handlerName = config.handler;\r\n  \r\n  if (notificationHandlers[handlerName]) {\r\n    switch (handlerName) {\r\n      case 'showMissingResumeModal':\r\n        notificationHandlers.showMissingResumeModal();\r\n        break;\r\n      case 'showSessionExpiredModal':\r\n        notificationHandlers.showSessionExpiredModal();\r\n        break;\r\n      case 'showMaintenanceModal':\r\n        notificationHandlers.showMaintenanceModal();\r\n        break;\r\n      case 'showValidationError':\r\n        const errorData = error?.response?.data || {};\r\n        notificationHandlers.showValidationError(\r\n          `Validation Error ${context ? `in ${context}` : ''}`, \r\n          errorData\r\n        );\r\n        break;\r\n      case 'showAuthError':\r\n        const message = error?.response?.data?.detail || \r\n                       error?.response?.data?.message || \r\n                       `Authentication failed${context ? ` while ${context}` : ''}`;\r\n        notificationHandlers.showAuthError(message);\r\n        break;\r\n      case 'showFileUploadError':\r\n        notificationHandlers.showFileUploadError();\r\n        break;\r\n      case 'showNetworkError':\r\n        notificationHandlers.showNetworkError(error);\r\n        break;\r\n      case 'showProfileIncompleteModal':\r\n        notificationHandlers.showProfileIncompleteModal();\r\n        break;\r\n      default:\r\n        return handleGenericError(error, context, notificationHandlers);\r\n    }\r\n    return true; // Error was handled\r\n  }\r\n  \r\n  return false; // Error not handled\r\n};\r\n\r\nconst handleGenericError = (error, context, notificationHandlers) => {\r\n  if (notificationHandlers.handleApiError) {\r\n    notificationHandlers.handleApiError(error, context);\r\n    return true;\r\n  }\r\n  \r\n  // Ultimate fallback\r\n  console.error('Unhandled error:', error);\r\n  return false;\r\n};\r\n\r\n// Hook for easy error handling in components\r\nexport const useErrorHandler = () => {\r\n  const notificationHandlers = useNotification();\r\n  \r\n  const handleError = (error, context = '') => {\r\n    return detectAndHandleError(error, context, notificationHandlers);\r\n  };\r\n\r\n  return { handleError };\r\n};\r\n\r\n// Axios interceptor setup\r\nexport const setupErrorInterceptor = (axiosInstance, notificationHandlers) => {\r\n  axiosInstance.interceptors.response.use(\r\n    (response) => response,\r\n    (error) => {\r\n      // Automatically handle common errors\r\n      detectAndHandleError(error, 'API request', notificationHandlers);\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n};\r\n\r\nexport default {\r\n  detectAndHandleError,\r\n  useErrorHandler,\r\n  setupErrorInterceptor,\r\n  ERROR_PATTERNS\r\n}; "], "names": [], "mappings": ";;;;;;;AAAA;;;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB;QACd,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAgB;YAAkB;YAAS;SAAQ;QAC9D,SAAS;IACX;IACA,iBAAiB;QACf,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAW;YAAiB;SAAgB;QACvD,SAAS;IACX;IACA,mBAAmB;QACjB,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAc;YAAa;SAAgB;QACtD,SAAS;IACX;IACA,YAAY;QACV,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAc;YAAW;SAAW;QAC/C,SAAS;IACX;IACA,iBAAiB;QACf,QAAQ;YAAC;SAAS;QAClB,UAAU;YAAC;YAAU;YAAoB;SAAiC;QAC1E,SAAS;IACX;IACA,oBAAoB;QAClB,UAAU;YAAC;YAAsB;YAAmB;SAAiB;QACrE,SAAS;IACX;IACA,aAAa;QACX,UAAU;YAAC;YAAQ;YAAU;YAAQ;YAAU;SAAY;QAC3D,SAAS;IACX;IACA,eAAe;QACb,OAAO;YAAC;YAAiB;YAAgB;SAAc;QACvD,UAAU;YAAC;YAAW;YAAc;SAAU;QAC9C,SAAS;IACX;IACA,aAAa;QACX,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAe;YAAuB;SAA0B;QAC3E,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,CAAC,OAAO,UAAU,EAAE,EAAE;IACxD,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;IAC5C,MAAM,eAAe,CAAC,UAAU,MAAM,IAAI,UAAU,OAAO,IAAI,MAAM,OAAO,IAAI,EAAE,EAAE,WAAW;IAC/F,MAAM,aAAa,OAAO,UAAU;IAEpC,oCAAoC;IACpC,KAAK,MAAM,CAAC,SAAS,OAAO,IAAI,OAAO,OAAO,CAAC,gBAAiB;QAC9D,qBAAqB;QACrB,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,aAAa;YACrD,8CAA8C;YAC9C,IAAI,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;gBACvF;YACF;YAEA,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,gDAAgD;QAChD,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,CAAC,MAAM,GAAG;YAClE,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,kCAAkC;QAClC,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;YACtF,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;IACF;IAEA,qCAAqC;IACrC,OAAO,mBAAmB,OAAO,SAAS;AAC5C;AAEA,MAAM,sBAAsB,CAAC,SAAS,OAAO,SAAS;IACpD,MAAM,SAAS,cAAc,CAAC,QAAQ;IACtC,MAAM,cAAc,OAAO,OAAO;IAElC,IAAI,oBAAoB,CAAC,YAAY,EAAE;QACrC,OAAQ;YACN,KAAK;gBACH,qBAAqB,sBAAsB;gBAC3C;YACF,KAAK;gBACH,qBAAqB,uBAAuB;gBAC5C;YACF,KAAK;gBACH,qBAAqB,oBAAoB;gBACzC;YACF,KAAK;gBACH,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;gBAC5C,qBAAqB,mBAAmB,CACtC,CAAC,iBAAiB,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI,EACpD;gBAEF;YACF,KAAK;gBACH,MAAM,UAAU,OAAO,UAAU,MAAM,UACxB,OAAO,UAAU,MAAM,WACvB,CAAC,qBAAqB,EAAE,UAAU,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI;gBAC3E,qBAAqB,aAAa,CAAC;gBACnC;YACF,KAAK;gBACH,qBAAqB,mBAAmB;gBACxC;YACF,KAAK;gBACH,qBAAqB,gBAAgB,CAAC;gBACtC;YACF,KAAK;gBACH,qBAAqB,0BAA0B;gBAC/C;YACF;gBACE,OAAO,mBAAmB,OAAO,SAAS;QAC9C;QACA,OAAO,MAAM,oBAAoB;IACnC;IAEA,OAAO,OAAO,oBAAoB;AACpC;AAEA,MAAM,qBAAqB,CAAC,OAAO,SAAS;IAC1C,IAAI,qBAAqB,cAAc,EAAE;QACvC,qBAAqB,cAAc,CAAC,OAAO;QAC3C,OAAO;IACT;IAEA,oBAAoB;IACpB,QAAQ,KAAK,CAAC,oBAAoB;IAClC,OAAO;AACT;AAGO,MAAM,kBAAkB;;IAC7B,MAAM,uBAAuB,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IAE3C,MAAM,cAAc,CAAC,OAAO,UAAU,EAAE;QACtC,OAAO,qBAAqB,OAAO,SAAS;IAC9C;IAEA,OAAO;QAAE;IAAY;AACvB;GARa;;QACkB,0IAAA,CAAA,kBAAe;;;AAUvC,MAAM,wBAAwB,CAAC,eAAe;IACnD,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG;qCACrC,CAAC,WAAa;;qCACd,CAAC;YACC,qCAAqC;YACrC,qBAAqB,OAAO,eAAe;YAC3C,OAAO,QAAQ,MAAM,CAAC;QACxB;;AAEJ;uCAEe;IACb;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/client.js"], "sourcesContent": ["import axios from 'axios';\r\nimport { setupErrorInterceptor } from './errorHandler';\r\n\r\nconst client = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add a request interceptor to include the auth token\r\nclient.interceptors.request.use(\r\n  (config) => {\r\n    // Get the token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n    \r\n    // If token exists, add it to the Authorization header\r\n    if (token) {\r\n      config.headers['Authorization'] = `Bearer ${token}`;\r\n    }\r\n    \r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add a response interceptor to handle 401 errors (token expired)\r\nclient.interceptors.response.use(\r\n  (response) => response,\r\n  async (error) => {\r\n    const originalRequest = error.config;\r\n    \r\n    // If error is 401 and we haven't tried to refresh the token yet\r\n    if (error.response?.status === 401 && !originalRequest._retry) {\r\n      originalRequest._retry = true;\r\n      \r\n      try {\r\n        // Get refresh token\r\n        const refreshToken = localStorage.getItem('refresh_token');\r\n        \r\n        if (refreshToken) {\r\n          // Try to get a new token\r\n          const response = await axios.post('http://127.0.0.1:8000/api/auth/token/refresh/', {\r\n            refresh: refreshToken\r\n          });\r\n          \r\n          // Store the new tokens\r\n          localStorage.setItem('access_token', response.data.access);\r\n          \r\n          // Update the Authorization header\r\n          originalRequest.headers['Authorization'] = `Bearer ${response.data.access}`;\r\n          \r\n          // Retry the original request\r\n          return client(originalRequest);\r\n        }\r\n      } catch (refreshError) {\r\n        console.error('Error refreshing token:', refreshError);\r\n        \r\n        // If token refresh fails, redirect to login\r\n        if (typeof window !== 'undefined') {\r\n          // Clear tokens\r\n          localStorage.removeItem('access_token');\r\n          localStorage.removeItem('refresh_token');\r\n          \r\n          // Redirect to login page\r\n          window.location.href = '/login';\r\n        }\r\n      }\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default client;\r\n"], "names": [], "mappings": ";;;AAIW;AAJX;AACA;;;AAEA,MAAM,SAAS,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC1B,SAAS,6DAAmC;IAC5C,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,sDAAsD;AACtD,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG,CAC7B,CAAC;IACC,kCAAkC;IAClC,MAAM,QAAQ,aAAa,OAAO,CAAC;IAEnC,sDAAsD;IACtD,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACrD;IAEA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,kEAAkE;AAClE,OAAO,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC9B,CAAC,WAAa,UACd,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAEpC,gEAAgE;IAChE,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;QAC7D,gBAAgB,MAAM,GAAG;QAEzB,IAAI;YACF,oBAAoB;YACpB,MAAM,eAAe,aAAa,OAAO,CAAC;YAE1C,IAAI,cAAc;gBAChB,yBAAyB;gBACzB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,iDAAiD;oBACjF,SAAS;gBACX;gBAEA,uBAAuB;gBACvB,aAAa,OAAO,CAAC,gBAAgB,SAAS,IAAI,CAAC,MAAM;gBAEzD,kCAAkC;gBAClC,gBAAgB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,MAAM,EAAE;gBAE3E,6BAA6B;gBAC7B,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,4CAA4C;YAC5C,wCAAmC;gBACjC,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBAExB,yBAAyB;gBACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;IACF;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/jobs.js"], "sourcesContent": ["import client from './client';\r\n\r\n// List all jobs with pagination and filtering\r\nexport function listJobs(params = {}) {\r\n  const queryParams = new URLSearchParams();\r\n  \r\n  // Add pagination parameters\r\n  if (params.page) queryParams.append('page', params.page);\r\n  if (params.per_page) queryParams.append('per_page', params.per_page);\r\n  \r\n  // Add filtering parameters\r\n  if (params.job_type && params.job_type !== 'ALL') queryParams.append('job_type', params.job_type);\r\n  if (params.location && params.location !== 'ALL') queryParams.append('location', params.location);\r\n  if (params.salary_min) queryParams.append('salary_min', params.salary_min);\r\n  if (params.search) queryParams.append('search', params.search);\r\n  \r\n  const queryString = queryParams.toString();\r\n  const url = `/api/v1/college/default-college/jobs/${queryString ? `?${queryString}` : ''}`;\r\n  \r\n  return client.get(url);\r\n}\r\n\r\n// Apply to a job\r\nexport function applyToJob(job, coverLetter, additionalFields = {}) {\r\n  // Check if any additional fields contain files\r\n  const hasFiles = Object.values(additionalFields).some(value => value instanceof File);\r\n\r\n  if (hasFiles) {\r\n    // Use FormData for file uploads\r\n    const formData = new FormData();\r\n    formData.append('cover_letter', coverLetter);\r\n\r\n    // Handle additional fields with files\r\n    Object.entries(additionalFields).forEach(([key, value]) => {\r\n      if (value instanceof File) {\r\n        formData.append(key, value);\r\n      } else {\r\n        formData.append(key, JSON.stringify(value));\r\n      }\r\n    });\r\n\r\n    return client.post(`/api/v1/college/default-college/jobs/${job}/apply/`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n  } else {\r\n    // Use JSON for non-file submissions\r\n    return client.post(`/api/v1/college/default-college/jobs/${job}/apply/`, {\r\n      cover_letter: coverLetter,\r\n      additional_field_responses: additionalFields\r\n    });\r\n  }\r\n}\r\n\r\n// Get job details by ID\r\nexport function getJobById(jobId) {\r\n  return client.get(`/api/v1/college/default-college/jobs/${jobId}/`);\r\n}\r\n\r\n// List jobs the current student has applied to\r\nexport function listAppliedJobs() {\r\n  return client.get('/api/v1/college/default-college/jobs/applied/');\r\n}\r\n\r\n// Admin API functions for managing jobs\r\n\r\n// Note: Company management functions moved to /api/companies.js to avoid conflicts\r\n\r\n// Create a new job posting\r\nexport function createJob(jobData) {\r\n  return client.post('/api/v1/college/default-college/jobs/create/', jobData);\r\n}\r\n\r\n// Update job posting\r\nexport function updateJob(jobId, jobData) {\r\n  return client.put(`/api/v1/college/default-college/jobs/${jobId}/`, jobData);\r\n}\r\n\r\n// Delete job posting\r\nexport function deleteJob(jobId) {\r\n  return client.delete(`/api/v1/college/default-college/jobs/${jobId}/`);\r\n}\r\n\r\n// Get job applications for admin\r\nexport function getJobApplications(jobId) {\r\n  return client.get(`/api/v1/college/default-college/jobs/${jobId}/applications/`);\r\n}\r\n\r\n// Get all applications for admin dashboard\r\nexport function getAllApplications() {\r\n  return client.get('/api/v1/college/default-college/applications/');\r\n}\r\n\r\n// Admin-specific job listing (shows all jobs including unpublished)\r\nexport function listJobsAdmin(params = {}) {\r\n  const queryParams = new URLSearchParams();\r\n  \r\n  // Add pagination parameters\r\n  if (params.page) queryParams.append('page', params.page);\r\n  if (params.per_page) queryParams.append('per_page', params.per_page);\r\n  \r\n  // Add filtering parameters\r\n  if (params.search) queryParams.append('search', params.search);\r\n  if (params.type && params.type !== 'All') queryParams.append('job_type', params.type);\r\n  if (params.minCTC) queryParams.append('salary_min', params.minCTC);\r\n  if (params.maxCTC) queryParams.append('salary_max', params.maxCTC);\r\n  if (params.minStipend) queryParams.append('stipend_min', params.minStipend);\r\n  if (params.maxStipend) queryParams.append('stipend_max', params.maxStipend);\r\n  if (params.location) queryParams.append('location', params.location);\r\n  if (params.is_published !== undefined) queryParams.append('is_published', params.is_published);\r\n  \r\n  // Add company filtering\r\n  if (params.company_id) queryParams.append('company_id', params.company_id);\r\n  if (params.company_name) queryParams.append('company_name', params.company_name);\r\n  \r\n  const queryString = queryParams.toString();\r\n  const url = `/api/v1/college/default-college/jobs/admin/${queryString ? `?${queryString}` : ''}`;\r\n  \r\n  console.log('🌐 listJobsAdmin calling URL:', url, 'with params:', params);\r\n  \r\n  return client.get(url).then(response => {\r\n    console.log('🌐 listJobsAdmin response:', {\r\n      status: response.status,\r\n      totalJobs: response.data?.pagination?.total_count || 0,\r\n      currentPage: response.data?.pagination?.current_page || 1,\r\n      totalPages: response.data?.pagination?.total_pages || 1\r\n    });\r\n    return response;\r\n  }).catch(error => {\r\n    console.error('🌐 listJobsAdmin error:', error);\r\n    console.error('🌐 listJobsAdmin error response:', error.response?.data);\r\n    throw error;\r\n  });\r\n}\r\n\r\n// Toggle job publish status\r\nexport function toggleJobPublish(jobId) {\r\n  return client.patch(`/api/v1/jobs/${jobId}/toggle-publish/`);\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAGO,SAAS,SAAS,SAAS,CAAC,CAAC;IAClC,MAAM,cAAc,IAAI;IAExB,4BAA4B;IAC5B,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;IACvD,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAEnE,2BAA2B;IAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,KAAK,OAAO,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAChG,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,KAAK,OAAO,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAChG,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;IACzE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IAE7D,MAAM,cAAc,YAAY,QAAQ;IACxC,MAAM,MAAM,CAAC,qCAAqC,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAE1F,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAGO,SAAS,WAAW,GAAG,EAAE,WAAW,EAAE,mBAAmB,CAAC,CAAC;IAChE,+CAA+C;IAC/C,MAAM,WAAW,OAAO,MAAM,CAAC,kBAAkB,IAAI,CAAC,CAAA,QAAS,iBAAiB;IAEhF,IAAI,UAAU;QACZ,gCAAgC;QAChC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,gBAAgB;QAEhC,sCAAsC;QACtC,OAAO,OAAO,CAAC,kBAAkB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACpD,IAAI,iBAAiB,MAAM;gBACzB,SAAS,MAAM,CAAC,KAAK;YACvB,OAAO;gBACL,SAAS,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC;YACtC;QACF;QAEA,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,IAAI,OAAO,CAAC,EAAE,UAAU;YACjF,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,OAAO;QACL,oCAAoC;QACpC,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,IAAI,OAAO,CAAC,EAAE;YACvE,cAAc;YACd,4BAA4B;QAC9B;IACF;AACF;AAGO,SAAS,WAAW,KAAK;IAC9B,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAC;AACpE;AAGO,SAAS;IACd,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAOO,SAAS,UAAU,OAAO;IAC/B,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,gDAAgD;AACrE;AAGO,SAAS,UAAU,KAAK,EAAE,OAAO;IACtC,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAC,EAAE;AACtE;AAGO,SAAS,UAAU,KAAK;IAC7B,OAAO,uHAAA,CAAA,UAAM,CAAC,MAAM,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAC;AACvE;AAGO,SAAS,mBAAmB,KAAK;IACtC,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,cAAc,CAAC;AACjF;AAGO,SAAS;IACd,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAGO,SAAS,cAAc,SAAS,CAAC,CAAC;IACvC,MAAM,cAAc,IAAI;IAExB,4BAA4B;IAC5B,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;IACvD,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAEnE,2BAA2B;IAC3B,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IAC7D,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,OAAO,YAAY,MAAM,CAAC,YAAY,OAAO,IAAI;IACpF,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,MAAM;IACjE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,MAAM;IACjE,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,eAAe,OAAO,UAAU;IAC1E,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,eAAe,OAAO,UAAU;IAC1E,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IACnE,IAAI,OAAO,YAAY,KAAK,WAAW,YAAY,MAAM,CAAC,gBAAgB,OAAO,YAAY;IAE7F,wBAAwB;IACxB,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;IACzE,IAAI,OAAO,YAAY,EAAE,YAAY,MAAM,CAAC,gBAAgB,OAAO,YAAY;IAE/E,MAAM,cAAc,YAAY,QAAQ;IACxC,MAAM,MAAM,CAAC,2CAA2C,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAEhG,QAAQ,GAAG,CAAC,iCAAiC,KAAK,gBAAgB;IAElE,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAA;QAC1B,QAAQ,GAAG,CAAC,8BAA8B;YACxC,QAAQ,SAAS,MAAM;YACvB,WAAW,SAAS,IAAI,EAAE,YAAY,eAAe;YACrD,aAAa,SAAS,IAAI,EAAE,YAAY,gBAAgB;YACxD,YAAY,SAAS,IAAI,EAAE,YAAY,eAAe;QACxD;QACA,OAAO;IACT,GAAG,KAAK,CAAC,CAAA;QACP,QAAQ,KAAK,CAAC,2BAA2B;QACzC,QAAQ,KAAK,CAAC,oCAAoC,MAAM,QAAQ,EAAE;QAClE,MAAM;IACR;AACF;AAGO,SAAS,iBAAiB,KAAK;IACpC,OAAO,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC7D", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/jobs/%5Bid%5D/page.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { useRouter, useParams } from 'next/navigation';\r\nimport { use } from 'react';\r\nimport { ArrowLeft, Edit, Send, X, MapPin, Calendar, DollarSign, Briefcase, Users, Eye } from \"lucide-react\";\r\nimport { FormattedJobDescription } from '../../../../lib/utils';\r\nimport { getJobById } from '../../../../api/jobs';\r\n\r\nexport default function ViewJob({ params }) {\r\n  const unwrappedParams = use(params);\r\n  const jobId = parseInt(unwrappedParams.id);\r\n  const router = useRouter();\r\n  \r\n  const [job, setJob] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const loadJobData = async () => {\r\n      try {\r\n        console.log('Loading job data for ID:', jobId);\r\n        const response = await getJobById(jobId);\r\n        console.log('Job API response:', response);\r\n        \r\n        if (response.data) {\r\n          const jobData = response.data;\r\n          console.log('Setting job data:', jobData);\r\n          \r\n          // Format the data for display\r\n          const formattedJob = {\r\n            id: jobData.id,\r\n            title: jobData.title || 'No title',\r\n            description: jobData.description || 'No description available',\r\n            location: jobData.location || 'Not specified',\r\n            job_type: jobData.job_type || 'Not specified',\r\n            salary_min: jobData.salary_min || 0,\r\n            salary_max: jobData.salary_max || 0,\r\n            application_deadline: jobData.application_deadline || new Date().toISOString().split('T')[0],\r\n            is_published: jobData.is_published || false,\r\n            company_name: jobData.company_name || jobData.company?.name || 'Unknown Company',\r\n            requirements: Array.isArray(jobData.requirements) ? jobData.requirements : \r\n                         typeof jobData.requirements === 'string' ? jobData.requirements.split(',').map(r => r.trim()) : [],\r\n            skills: Array.isArray(jobData.skills) ? jobData.skills : \r\n                   typeof jobData.skills === 'string' ? jobData.skills.split(',').map(s => s.trim()) : [],\r\n            created_at: jobData.created_at || new Date().toISOString(),\r\n            updated_at: jobData.updated_at || new Date().toISOString(),\r\n            applications_count: jobData.applications_count || 0,\r\n            duration: jobData.duration || 'Not specified',\r\n            company_id: jobData.company_id || jobData.company?.id || null\r\n          };\r\n          \r\n          setJob(formattedJob);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading job:', error);\r\n        setError('Failed to load job data. Please try again.');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (jobId) {\r\n      loadJobData();\r\n    }\r\n  }, [jobId]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"h-full overflow-y-auto\">\r\n        <div className=\"p-6\">\r\n          <div className=\"flex items-center justify-center h-64\">\r\n            <div className=\"text-lg\">Loading job details...</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"h-full overflow-y-auto\">\r\n        <div className=\"p-6\">\r\n          <div className=\"flex items-center justify-center h-64\">\r\n            <div className=\"text-red-600\">Error: {error}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!job) {\r\n    return (\r\n      <div className=\"h-full overflow-y-auto\">\r\n        <div className=\"p-6\">\r\n          <div className=\"flex items-center justify-center h-64\">\r\n            <div className=\"text-gray-600\">Job not found</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-full overflow-y-auto\">\r\n      <div className=\"p-6\">\r\n        {/* Header */}\r\n        <div className=\"mb-8\">\r\n          <button \r\n            onClick={() => router.back()}\r\n            className=\"flex items-center text-gray-600 hover:text-gray-900 mb-4\"\r\n          >\r\n            <ArrowLeft className=\"w-5 h-5 mr-2\" />\r\n            Back to Company Management\r\n          </button>\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <h1 className=\"text-3xl font-bold text-gray-900\">{job.title}</h1>\r\n              <p className=\"text-gray-600 mt-2\">\r\n                {job.company_name} • Job ID: {jobId}\r\n              </p>\r\n            </div>\r\n            <div className=\"flex space-x-3\">\r\n              <span className={`px-3 py-1 rounded-full text-sm font-medium ${\r\n                job.is_published \r\n                  ? 'bg-green-100 text-green-800' \r\n                  : 'bg-yellow-100 text-yellow-800'\r\n              }`}>\r\n                {job.is_published ? 'Published' : 'Draft'}\r\n              </span>\r\n              <button \r\n                onClick={() => router.push(`/admin/jobs/edit/${jobId}`)}\r\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center\"\r\n              >\r\n                <Edit className=\"w-4 h-4 mr-2\" />\r\n                Edit Job\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n          {/* Main Content */}\r\n          <div className=\"lg:col-span-2 space-y-6\">\r\n            {/* Job Details */}\r\n            <div className=\"bg-white shadow-sm rounded-lg p-6\">\r\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Job Details</h2>\r\n              \r\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <MapPin className=\"w-5 h-5 text-gray-400\" />\r\n                  <div>\r\n                    <p className=\"text-sm text-gray-500\">Location</p>\r\n                    <p className=\"font-medium\">{job.location}</p>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"flex items-center space-x-2\">\r\n                  <Briefcase className=\"w-5 h-5 text-gray-400\" />\r\n                  <div>\r\n                    <p className=\"text-sm text-gray-500\">Type</p>\r\n                    <p className=\"font-medium\">{job.job_type}</p>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"flex items-center space-x-2\">\r\n                  <DollarSign className=\"w-5 h-5 text-gray-400\" />\r\n                  <div>\r\n                    <p className=\"text-sm text-gray-500\">Salary</p>\r\n                    <p className=\"font-medium\">\r\n                      {job.salary_min && job.salary_max \r\n                        ? `$${job.salary_min?.toLocaleString()} - $${job.salary_max?.toLocaleString()}`\r\n                        : 'Not specified'\r\n                      }\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"flex items-center space-x-2\">\r\n                  <Calendar className=\"w-5 h-5 text-gray-400\" />\r\n                  <div>\r\n                    <p className=\"text-sm text-gray-500\">Deadline</p>\r\n                    <p className=\"font-medium\">\r\n                      {job.application_deadline \r\n                        ? new Date(job.application_deadline).toLocaleDateString()\r\n                        : 'No deadline'\r\n                      }\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              {job.duration && (\r\n                <div className=\"mb-6\">\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <Calendar className=\"w-5 h-5 text-gray-400\" />\r\n                    <div>\r\n                      <p className=\"text-sm text-gray-500\">Duration</p>\r\n                      <p className=\"font-medium\">{job.duration}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n              \r\n              <div>\r\n                <h3 className=\"font-medium text-gray-900 mb-3\">Description</h3>\r\n                <FormattedJobDescription \r\n                  description={job.description} \r\n                  className=\"\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Requirements */}\r\n            {job.requirements && job.requirements.length > 0 && (\r\n              <div className=\"bg-white shadow-sm rounded-lg p-6\">\r\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Requirements</h2>\r\n                <ul className=\"space-y-2\">\r\n                  {job.requirements.map((requirement, index) => (\r\n                    <li key={index} className=\"flex items-start space-x-2\">\r\n                      <span className=\"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0\"></span>\r\n                      <span className=\"text-gray-700\">{requirement}</span>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n            )}\r\n\r\n            {/* Skills */}\r\n            {job.skills && job.skills.length > 0 && (\r\n              <div className=\"bg-white shadow-sm rounded-lg p-6\">\r\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Required Skills</h2>\r\n                <div className=\"flex flex-wrap gap-2\">\r\n                  {job.skills.map((skill, index) => (\r\n                    <span \r\n                      key={index}\r\n                      className=\"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium\"\r\n                    >\r\n                      {skill}\r\n                    </span>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Sidebar */}\r\n          <div className=\"space-y-6\">\r\n            {/* Stats */}\r\n            <div className=\"bg-white shadow-sm rounded-lg p-6\">\r\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Job Statistics</h3>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-gray-600\">Applications</span>\r\n                  <span className=\"font-semibold text-gray-900\">{job.applications_count}</span>\r\n                </div>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-gray-600\">Created</span>\r\n                  <span className=\"font-semibold text-gray-900\">\r\n                    {new Date(job.created_at).toLocaleDateString()}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-gray-600\">Last Updated</span>\r\n                  <span className=\"font-semibold text-gray-900\">\r\n                    {new Date(job.updated_at).toLocaleDateString()}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-gray-600\">Status</span>\r\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\r\n                    job.is_published \r\n                      ? 'bg-green-100 text-green-800' \r\n                      : 'bg-yellow-100 text-yellow-800'\r\n                  }`}>\r\n                    {job.is_published ? 'Published' : 'Draft'}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Actions */}\r\n            <div className=\"bg-white shadow-sm rounded-lg p-6\">\r\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Actions</h3>\r\n              <div className=\"space-y-3\">\r\n                <button \r\n                  onClick={() => router.push(`/admin/jobs/edit/${jobId}`)}\r\n                  className=\"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center\"\r\n                >\r\n                  <Edit className=\"w-4 h-4 mr-2\" />\r\n                  Edit Job\r\n                </button>\r\n                <button \r\n                  onClick={() => router.push(`/admin/applications?job_id=${jobId}`)}\r\n                  className=\"w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center justify-center\"\r\n                >\r\n                  <Eye className=\"w-4 h-4 mr-2\" />\r\n                  View Applications\r\n                </button>\r\n                <button className=\"w-full bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center justify-center\">\r\n                  <Send className=\"w-4 h-4 mr-2\" />\r\n                  Send Message\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Quick Info */}\r\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\r\n              <h4 className=\"font-semibold text-blue-900 mb-2\">Quick Info</h4>\r\n              <div className=\"space-y-2 text-sm\">\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-blue-700\">Job ID:</span>\r\n                  <span className=\"font-medium text-blue-900\">{job.id}</span>\r\n                </div>\r\n                {job.company_id && (\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-blue-700\">Company ID:</span>\r\n                    <span className=\"font-medium text-blue-900\">{job.company_id}</span>\r\n                  </div>\r\n                )}\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-blue-700\">Type:</span>\r\n                  <span className=\"font-medium text-blue-900\">{job.job_type}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS,QAAQ,EAAE,MAAM,EAAE;;IACxC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,MAAG,AAAD,EAAE;IAC5B,MAAM,QAAQ,SAAS,gBAAgB,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;iDAAc;oBAClB,IAAI;wBACF,QAAQ,GAAG,CAAC,4BAA4B;wBACxC,MAAM,WAAW,MAAM,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE;wBAClC,QAAQ,GAAG,CAAC,qBAAqB;wBAEjC,IAAI,SAAS,IAAI,EAAE;4BACjB,MAAM,UAAU,SAAS,IAAI;4BAC7B,QAAQ,GAAG,CAAC,qBAAqB;4BAEjC,8BAA8B;4BAC9B,MAAM,eAAe;gCACnB,IAAI,QAAQ,EAAE;gCACd,OAAO,QAAQ,KAAK,IAAI;gCACxB,aAAa,QAAQ,WAAW,IAAI;gCACpC,UAAU,QAAQ,QAAQ,IAAI;gCAC9B,UAAU,QAAQ,QAAQ,IAAI;gCAC9B,YAAY,QAAQ,UAAU,IAAI;gCAClC,YAAY,QAAQ,UAAU,IAAI;gCAClC,sBAAsB,QAAQ,oBAAoB,IAAI,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gCAC5F,cAAc,QAAQ,YAAY,IAAI;gCACtC,cAAc,QAAQ,YAAY,IAAI,QAAQ,OAAO,EAAE,QAAQ;gCAC/D,cAAc,MAAM,OAAO,CAAC,QAAQ,YAAY,IAAI,QAAQ,YAAY,GAC3D,OAAO,QAAQ,YAAY,KAAK,WAAW,QAAQ,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG;qEAAC,CAAA,IAAK,EAAE,IAAI;sEAAM,EAAE;gCAC/G,QAAQ,MAAM,OAAO,CAAC,QAAQ,MAAM,IAAI,QAAQ,MAAM,GAC/C,OAAO,QAAQ,MAAM,KAAK,WAAW,QAAQ,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG;qEAAC,CAAA,IAAK,EAAE,IAAI;sEAAM,EAAE;gCAC7F,YAAY,QAAQ,UAAU,IAAI,IAAI,OAAO,WAAW;gCACxD,YAAY,QAAQ,UAAU,IAAI,IAAI,OAAO,WAAW;gCACxD,oBAAoB,QAAQ,kBAAkB,IAAI;gCAClD,UAAU,QAAQ,QAAQ,IAAI;gCAC9B,YAAY,QAAQ,UAAU,IAAI,QAAQ,OAAO,EAAE,MAAM;4BAC3D;4BAEA,OAAO;wBACT;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,sBAAsB;wBACpC,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA,IAAI,OAAO;gBACT;YACF;QACF;4BAAG;QAAC;KAAM;IAEV,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCAAU;;;;;;;;;;;;;;;;;;;;;IAKnC;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BAAe;4BAAQ;;;;;;;;;;;;;;;;;;;;;;IAKhD;IAEA,IAAI,CAAC,KAAK;QACR,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;IAKzC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGxC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoC,IAAI,KAAK;;;;;;sDAC3D,6LAAC;4CAAE,WAAU;;gDACV,IAAI,YAAY;gDAAC;gDAAY;;;;;;;;;;;;;8CAGlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAW,CAAC,2CAA2C,EAC3D,IAAI,YAAY,GACZ,gCACA,iCACJ;sDACC,IAAI,YAAY,GAAG,cAAc;;;;;;sDAEpC,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,OAAO;4CACtD,WAAU;;8DAEV,6LAAC,8MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAe,IAAI,QAAQ;;;;;;;;;;;;;;;;;;8DAI5C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+MAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAe,IAAI,QAAQ;;;;;;;;;;;;;;;;;;8DAI5C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EACV,IAAI,UAAU,IAAI,IAAI,UAAU,GAC7B,CAAC,CAAC,EAAE,IAAI,UAAU,EAAE,iBAAiB,IAAI,EAAE,IAAI,UAAU,EAAE,kBAAkB,GAC7E;;;;;;;;;;;;;;;;;;8DAMV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EACV,IAAI,oBAAoB,GACrB,IAAI,KAAK,IAAI,oBAAoB,EAAE,kBAAkB,KACrD;;;;;;;;;;;;;;;;;;;;;;;;wCAOX,IAAI,QAAQ,kBACX,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAe,IAAI,QAAQ;;;;;;;;;;;;;;;;;;;;;;;sDAMhD,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC,sHAAA,CAAA,0BAAuB;oDACtB,aAAa,IAAI,WAAW;oDAC5B,WAAU;;;;;;;;;;;;;;;;;;gCAMf,IAAI,YAAY,IAAI,IAAI,YAAY,CAAC,MAAM,GAAG,mBAC7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAG,WAAU;sDACX,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBAClC,6LAAC;oDAAe,WAAU;;sEACxB,6LAAC;4DAAK,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;mDAF1B;;;;;;;;;;;;;;;;gCAUhB,IAAI,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,mBACjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;sDACZ,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACtB,6LAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;;;;;;;;sCAYjB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAA+B,IAAI,kBAAkB;;;;;;;;;;;;8DAEvE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEACb,IAAI,KAAK,IAAI,UAAU,EAAE,kBAAkB;;;;;;;;;;;;8DAGhD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEACb,IAAI,KAAK,IAAI,UAAU,EAAE,kBAAkB;;;;;;;;;;;;8DAGhD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAW,CAAC,2CAA2C,EAC3D,IAAI,YAAY,GACZ,gCACA,iCACJ;sEACC,IAAI,YAAY,GAAG,cAAc;;;;;;;;;;;;;;;;;;;;;;;;8CAO1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,OAAO;oDACtD,WAAU;;sEAEV,6LAAC,8MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGnC,6LAAC;oDACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,2BAA2B,EAAE,OAAO;oDAChE,WAAU;;sEAEV,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGlC,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;8CAOvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAA6B,IAAI,EAAE;;;;;;;;;;;;gDAEpD,IAAI,UAAU,kBACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAA6B,IAAI,UAAU;;;;;;;;;;;;8DAG/D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAA6B,IAAI,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3E;GAlUwB;;QAGP,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}