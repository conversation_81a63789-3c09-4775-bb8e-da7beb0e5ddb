(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/api/errorHandler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ERROR_PATTERNS": (()=>ERROR_PATTERNS),
    "default": (()=>__TURBOPACK__default__export__),
    "detectAndHandleError": (()=>detectAndHandleError),
    "setupErrorInterceptor": (()=>setupErrorInterceptor),
    "useErrorHandler": (()=>useErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/NotificationContext.jsx [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
const ERROR_PATTERNS = {
    AUTHENTICATION: {
        codes: [
            401
        ],
        keywords: [
            'unauthorized',
            'authentication',
            'token',
            'login'
        ],
        handler: 'showAuthError'
    },
    SESSION_EXPIRED: {
        codes: [
            401
        ],
        keywords: [
            'expired',
            'invalid token',
            'token expired'
        ],
        handler: 'showSessionExpiredModal'
    },
    PERMISSION_DENIED: {
        codes: [
            403
        ],
        keywords: [
            'permission',
            'forbidden',
            'access denied'
        ],
        handler: 'showAuthError'
    },
    VALIDATION: {
        codes: [
            400,
            422
        ],
        keywords: [
            'validation',
            'invalid',
            'required'
        ],
        handler: 'showValidationError'
    },
    RESUME_REQUIRED: {
        fields: [
            'resume'
        ],
        keywords: [
            'resume',
            'must be uploaded',
            'present in the student profile'
        ],
        handler: 'showMissingResumeModal'
    },
    PROFILE_INCOMPLETE: {
        keywords: [
            'profile incomplete',
            'missing profile',
            'update profile'
        ],
        handler: 'showProfileIncompleteModal'
    },
    FILE_UPLOAD: {
        keywords: [
            'file',
            'upload',
            'size',
            'format',
            'extension'
        ],
        handler: 'showFileUploadError'
    },
    NETWORK_ERROR: {
        codes: [
            'NETWORK_ERROR',
            'ECONNREFUSED',
            'ERR_NETWORK'
        ],
        keywords: [
            'network',
            'connection',
            'timeout'
        ],
        handler: 'showNetworkError'
    },
    MAINTENANCE: {
        codes: [
            503,
            502
        ],
        keywords: [
            'maintenance',
            'service unavailable',
            'temporarily unavailable'
        ],
        handler: 'showMaintenanceModal'
    }
};
const detectAndHandleError = (error, context = '', notificationHandlers)=>{
    const errorData = error?.response?.data || {};
    const errorMessage = (errorData.detail || errorData.message || error.message || '').toLowerCase();
    const statusCode = error?.response?.status;
    // Check for specific error patterns
    for (const [pattern, config] of Object.entries(ERROR_PATTERNS)){
        // Check status codes
        if (config.codes && config.codes.includes(statusCode)) {
            // Additional keyword check for more precision
            if (config.keywords && !config.keywords.some((keyword)=>errorMessage.includes(keyword))) {
                continue;
            }
            return handleSpecificError(pattern, error, context, notificationHandlers);
        }
        // Check for field-specific errors (like resume)
        if (config.fields && config.fields.some((field)=>errorData[field])) {
            return handleSpecificError(pattern, error, context, notificationHandlers);
        }
        // Check keywords in error message
        if (config.keywords && config.keywords.some((keyword)=>errorMessage.includes(keyword))) {
            return handleSpecificError(pattern, error, context, notificationHandlers);
        }
    }
    // Fallback to generic error handling
    return handleGenericError(error, context, notificationHandlers);
};
const handleSpecificError = (pattern, error, context, notificationHandlers)=>{
    const config = ERROR_PATTERNS[pattern];
    const handlerName = config.handler;
    if (notificationHandlers[handlerName]) {
        switch(handlerName){
            case 'showMissingResumeModal':
                notificationHandlers.showMissingResumeModal();
                break;
            case 'showSessionExpiredModal':
                notificationHandlers.showSessionExpiredModal();
                break;
            case 'showMaintenanceModal':
                notificationHandlers.showMaintenanceModal();
                break;
            case 'showValidationError':
                const errorData = error?.response?.data || {};
                notificationHandlers.showValidationError(`Validation Error ${context ? `in ${context}` : ''}`, errorData);
                break;
            case 'showAuthError':
                const message = error?.response?.data?.detail || error?.response?.data?.message || `Authentication failed${context ? ` while ${context}` : ''}`;
                notificationHandlers.showAuthError(message);
                break;
            case 'showFileUploadError':
                notificationHandlers.showFileUploadError();
                break;
            case 'showNetworkError':
                notificationHandlers.showNetworkError(error);
                break;
            case 'showProfileIncompleteModal':
                notificationHandlers.showProfileIncompleteModal();
                break;
            default:
                return handleGenericError(error, context, notificationHandlers);
        }
        return true; // Error was handled
    }
    return false; // Error not handled
};
const handleGenericError = (error, context, notificationHandlers)=>{
    if (notificationHandlers.handleApiError) {
        notificationHandlers.handleApiError(error, context);
        return true;
    }
    // Ultimate fallback
    console.error('Unhandled error:', error);
    return false;
};
const useErrorHandler = ()=>{
    _s();
    const notificationHandlers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotification"])();
    const handleError = (error, context = '')=>{
        return detectAndHandleError(error, context, notificationHandlers);
    };
    return {
        handleError
    };
};
_s(useErrorHandler, "2+3vdp+Gj38tyC21Sdt5jR3pRwQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotification"]
    ];
});
const setupErrorInterceptor = (axiosInstance, notificationHandlers)=>{
    axiosInstance.interceptors.response.use({
        "setupErrorInterceptor.use": (response)=>response
    }["setupErrorInterceptor.use"], {
        "setupErrorInterceptor.use": (error)=>{
            // Automatically handle common errors
            detectAndHandleError(error, 'API request', notificationHandlers);
            return Promise.reject(error);
        }
    }["setupErrorInterceptor.use"]);
};
const __TURBOPACK__default__export__ = {
    detectAndHandleError,
    useErrorHandler,
    setupErrorInterceptor,
    ERROR_PATTERNS
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/api/client.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$errorHandler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/errorHandler.js [app-client] (ecmascript)");
;
;
const client = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "http://127.0.0.1:8000") || 'http://127.0.0.1:8000',
    headers: {
        'Content-Type': 'application/json'
    }
});
// Add a request interceptor to include the auth token
client.interceptors.request.use((config)=>{
    // Get the token from localStorage
    const token = localStorage.getItem('access_token');
    // If token exists, add it to the Authorization header
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Add a response interceptor to handle 401 errors (token expired)
client.interceptors.response.use((response)=>response, async (error)=>{
    const originalRequest = error.config;
    // If error is 401 and we haven't tried to refresh the token yet
    if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        try {
            // Get refresh token
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
                // Try to get a new token
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('http://127.0.0.1:8000/api/auth/token/refresh/', {
                    refresh: refreshToken
                });
                // Store the new tokens
                localStorage.setItem('access_token', response.data.access);
                // Update the Authorization header
                originalRequest.headers['Authorization'] = `Bearer ${response.data.access}`;
                // Retry the original request
                return client(originalRequest);
            }
        } catch (refreshError) {
            console.error('Error refreshing token:', refreshError);
            // If token refresh fails, redirect to login
            if ("TURBOPACK compile-time truthy", 1) {
                // Clear tokens
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                // Redirect to login page
                window.location.href = '/login';
            }
        }
    }
    return Promise.reject(error);
});
const __TURBOPACK__default__export__ = client;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/api/jobs.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "applyToJob": (()=>applyToJob),
    "createJob": (()=>createJob),
    "deleteJob": (()=>deleteJob),
    "getAllApplications": (()=>getAllApplications),
    "getJobApplications": (()=>getJobApplications),
    "getJobById": (()=>getJobById),
    "listAppliedJobs": (()=>listAppliedJobs),
    "listJobs": (()=>listJobs),
    "listJobsAdmin": (()=>listJobsAdmin),
    "toggleJobPublish": (()=>toggleJobPublish),
    "updateJob": (()=>updateJob)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/client.js [app-client] (ecmascript)");
;
function listJobs(params = {}) {
    const queryParams = new URLSearchParams();
    // Add pagination parameters
    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    // Add filtering parameters
    if (params.job_type && params.job_type !== 'ALL') queryParams.append('job_type', params.job_type);
    if (params.location && params.location !== 'ALL') queryParams.append('location', params.location);
    if (params.salary_min) queryParams.append('salary_min', params.salary_min);
    if (params.search) queryParams.append('search', params.search);
    const queryString = queryParams.toString();
    const url = `/api/v1/college/default-college/jobs/${queryString ? `?${queryString}` : ''}`;
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(url);
}
function applyToJob(job, coverLetter, additionalFields = {}) {
    // Check if any additional fields contain files
    const hasFiles = Object.values(additionalFields).some((value)=>value instanceof File);
    if (hasFiles) {
        // Use FormData for file uploads
        const formData = new FormData();
        formData.append('cover_letter', coverLetter);
        // Handle additional fields with files
        Object.entries(additionalFields).forEach(([key, value])=>{
            if (value instanceof File) {
                formData.append(key, value);
            } else {
                formData.append(key, JSON.stringify(value));
            }
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/college/default-college/jobs/${job}/apply/`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    } else {
        // Use JSON for non-file submissions
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/college/default-college/jobs/${job}/apply/`, {
            cover_letter: coverLetter,
            additional_field_responses: additionalFields
        });
    }
}
function getJobById(jobId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/college/default-college/jobs/${jobId}/`);
}
function listAppliedJobs() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/api/v1/college/default-college/jobs/applied/');
}
function createJob(jobData) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/api/v1/college/default-college/jobs/create/', jobData);
}
function updateJob(jobId, jobData) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/api/v1/college/default-college/jobs/${jobId}/`, jobData);
}
function deleteJob(jobId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/api/v1/college/default-college/jobs/${jobId}/`);
}
function getJobApplications(jobId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/college/default-college/jobs/${jobId}/applications/`);
}
function getAllApplications() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/api/v1/college/default-college/applications/');
}
function listJobsAdmin(params = {}) {
    const queryParams = new URLSearchParams();
    // Add pagination parameters
    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    // Add filtering parameters
    if (params.search) queryParams.append('search', params.search);
    if (params.type && params.type !== 'All') queryParams.append('job_type', params.type);
    if (params.minCTC) queryParams.append('salary_min', params.minCTC);
    if (params.maxCTC) queryParams.append('salary_max', params.maxCTC);
    if (params.minStipend) queryParams.append('stipend_min', params.minStipend);
    if (params.maxStipend) queryParams.append('stipend_max', params.maxStipend);
    if (params.location) queryParams.append('location', params.location);
    if (params.is_published !== undefined) queryParams.append('is_published', params.is_published);
    // Add company filtering
    if (params.company_id) queryParams.append('company_id', params.company_id);
    if (params.company_name) queryParams.append('company_name', params.company_name);
    const queryString = queryParams.toString();
    const url = `/api/v1/college/default-college/jobs/admin/${queryString ? `?${queryString}` : ''}`;
    console.log('🌐 listJobsAdmin calling URL:', url, 'with params:', params);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(url).then((response)=>{
        console.log('🌐 listJobsAdmin response:', {
            status: response.status,
            totalJobs: response.data?.pagination?.total_count || 0,
            currentPage: response.data?.pagination?.current_page || 1,
            totalPages: response.data?.pagination?.total_pages || 1
        });
        return response;
    }).catch((error)=>{
        console.error('🌐 listJobsAdmin error:', error);
        console.error('🌐 listJobsAdmin error response:', error.response?.data);
        throw error;
    });
}
function toggleJobPublish(jobId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/api/v1/jobs/${jobId}/toggle-publish/`);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/profileValidation.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Profile validation utilities for job applications
__turbopack_context__.s({
    "CRITICAL_FIELDS": (()=>CRITICAL_FIELDS),
    "REQUIRED_PROFILE_FIELDS": (()=>REQUIRED_PROFILE_FIELDS),
    "default": (()=>__TURBOPACK__default__export__),
    "getProfileCompletionSuggestions": (()=>getProfileCompletionSuggestions),
    "validateForJobApplication": (()=>validateForJobApplication),
    "validateProfile": (()=>validateProfile)
});
const REQUIRED_PROFILE_FIELDS = {
    basic: {
        first_name: 'First Name',
        last_name: 'Last Name',
        email: 'Email Address',
        phone: 'Phone Number',
        date_of_birth: 'Date of Birth'
    },
    academic: {
        student_id: 'Student ID/Roll Number',
        branch: 'Department/Branch',
        gpa: 'CGPA/GPA',
        joining_year: 'Joining Year',
        passout_year: 'Passout Year'
    },
    contact: {
        address: 'Address',
        city: 'City',
        state: 'State',
        pincode: 'PIN Code'
    },
    documents: {
        resume: 'Resume',
        tenth_certificate: 'Class 10 Certificate',
        twelfth_certificate: 'Class 12 Certificate'
    },
    education: {
        tenth_percentage: 'Class 10 Percentage',
        twelfth_percentage: 'Class 12 Percentage',
        tenth_year_of_passing: 'Class 10 Year of Passing',
        twelfth_year_of_passing: 'Class 12 Year of Passing'
    }
};
const CRITICAL_FIELDS = [
    'first_name',
    'last_name',
    'email',
    'phone',
    'student_id',
    'branch',
    'gpa',
    'resume'
];
const validateProfile = (profile)=>{
    const missing = [];
    const warnings = [];
    const errors = [];
    if (!profile) {
        return {
            isValid: false,
            missing: [
                'Profile not found'
            ],
            warnings: [],
            errors: [
                'Please complete your profile before applying'
            ],
            score: 0
        };
    }
    // Check critical fields
    CRITICAL_FIELDS.forEach((field)=>{
        const value = profile[field];
        const fieldName = getFieldDisplayName(field);
        if (!value || typeof value === 'string' && value.trim() === '') {
            missing.push(fieldName);
        }
    });
    // Check GPA/CGPA format and range
    if (profile.gpa) {
        const gpaValue = parseFloat(profile.gpa);
        if (isNaN(gpaValue) || gpaValue < 0 || gpaValue > 10) {
            errors.push('CGPA must be between 0 and 10');
        } else if (gpaValue < 6.0) {
            warnings.push('CGPA below 6.0 may limit job opportunities');
        }
    }
    // Check phone number format
    if (profile.phone) {
        const phoneRegex = /^[6-9]\d{9}$/;
        if (!phoneRegex.test(profile.phone.replace(/[^\d]/g, ''))) {
            errors.push('Phone number must be a valid 10-digit Indian mobile number');
        }
    }
    // Check email format
    if (profile.email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(profile.email)) {
            errors.push('Please provide a valid email address');
        }
    }
    // Check resume
    if (!profile.resume && !profile.resume_url) {
        missing.push('Resume');
        errors.push('Resume is required for job applications');
    }
    // Check academic details
    if (profile.tenth_percentage && profile.tenth_percentage < 60) {
        warnings.push('Class 10 percentage below 60% may limit opportunities');
    }
    if (profile.twelfth_percentage && profile.twelfth_percentage < 60) {
        warnings.push('Class 12 percentage below 60% may limit opportunities');
    }
    // Calculate completeness score
    const totalFields = Object.keys(REQUIRED_PROFILE_FIELDS).reduce((acc, section)=>acc + Object.keys(REQUIRED_PROFILE_FIELDS[section]).length, 0);
    const filledFields = totalFields - missing.length;
    const score = Math.round(filledFields / totalFields * 100);
    // Determine if profile is valid for job applications
    const isValid = missing.length === 0 && errors.length === 0;
    const canApply = CRITICAL_FIELDS.every((field)=>{
        const value = profile[field];
        return value && (typeof value !== 'string' || value.trim() !== '');
    });
    return {
        isValid,
        canApply,
        missing,
        warnings,
        errors,
        score,
        summary: generateSummary(score, missing.length, warnings.length, errors.length)
    };
};
const validateForJobApplication = (profile, jobRequirements = {})=>{
    const baseValidation = validateProfile(profile);
    // Additional job-specific validations
    const jobErrors = [];
    const jobWarnings = [];
    if (jobRequirements.minCgpa && profile.gpa) {
        const gpaValue = parseFloat(profile.gpa);
        if (gpaValue < jobRequirements.minCgpa) {
            jobErrors.push(`CGPA ${jobRequirements.minCgpa} or above required`);
        }
    }
    if (jobRequirements.allowedBranches && profile.branch) {
        if (!jobRequirements.allowedBranches.includes(profile.branch)) {
            jobErrors.push(`This job is not open for ${profile.branch} students`);
        }
    }
    if (jobRequirements.minTenthPercentage && profile.tenth_percentage) {
        if (profile.tenth_percentage < jobRequirements.minTenthPercentage) {
            jobErrors.push(`Class 10: ${jobRequirements.minTenthPercentage}% or above required`);
        }
    }
    if (jobRequirements.minTwelfthPercentage && profile.twelfth_percentage) {
        if (profile.twelfth_percentage < jobRequirements.minTwelfthPercentage) {
            jobErrors.push(`Class 12: ${jobRequirements.minTwelfthPercentage}% or above required`);
        }
    }
    return {
        ...baseValidation,
        jobSpecific: {
            errors: jobErrors,
            warnings: jobWarnings,
            isEligible: jobErrors.length === 0
        }
    };
};
const getProfileCompletionSuggestions = (validation)=>{
    const suggestions = [];
    if (validation.missing.includes('Resume')) {
        suggestions.push({
            type: 'critical',
            title: 'Upload Resume',
            description: 'A resume is required for all job applications',
            action: 'upload_resume',
            icon: 'upload'
        });
    }
    if (validation.missing.some((field)=>[
            'First Name',
            'Last Name',
            'Email',
            'Phone'
        ].includes(field))) {
        suggestions.push({
            type: 'critical',
            title: 'Complete Basic Information',
            description: 'Fill in your personal details',
            action: 'edit_basic_info',
            icon: 'user'
        });
    }
    if (validation.missing.some((field)=>[
            'CGPA/GPA',
            'Department/Branch'
        ].includes(field))) {
        suggestions.push({
            type: 'critical',
            title: 'Add Academic Details',
            description: 'Provide your academic information',
            action: 'edit_academic_info',
            icon: 'graduation-cap'
        });
    }
    if (validation.warnings.some((w)=>w.includes('percentage'))) {
        suggestions.push({
            type: 'warning',
            title: 'Review Academic Performance',
            description: 'Low grades may limit job opportunities',
            action: 'review_grades',
            icon: 'alert-triangle'
        });
    }
    if (validation.score < 80) {
        suggestions.push({
            type: 'info',
            title: 'Complete Profile',
            description: `Profile is ${validation.score}% complete. More complete profiles get better job matches.`,
            action: 'complete_profile',
            icon: 'info'
        });
    }
    return suggestions;
};
const getFieldDisplayName = (field)=>{
    for (const section of Object.values(REQUIRED_PROFILE_FIELDS)){
        if (section[field]) {
            return section[field];
        }
    }
    return field.replace(/_/g, ' ').replace(/\b\w/g, (l)=>l.toUpperCase());
};
const generateSummary = (score, missingCount, warningCount, errorCount)=>{
    if (errorCount > 0) {
        return {
            status: 'error',
            message: `${errorCount} error(s) need to be fixed before applying`,
            color: 'red'
        };
    }
    if (missingCount > 0) {
        return {
            status: 'incomplete',
            message: `${missingCount} required field(s) missing`,
            color: 'yellow'
        };
    }
    if (warningCount > 0) {
        return {
            status: 'warning',
            message: `Profile complete with ${warningCount} warning(s)`,
            color: 'orange'
        };
    }
    if (score >= 90) {
        return {
            status: 'excellent',
            message: 'Profile is excellent and ready for applications',
            color: 'green'
        };
    }
    if (score >= 80) {
        return {
            status: 'good',
            message: 'Profile is good for most applications',
            color: 'blue'
        };
    }
    return {
        status: 'needs_improvement',
        message: 'Profile needs more information for better job matching',
        color: 'yellow'
    };
};
const __TURBOPACK__default__export__ = {
    validateProfile,
    validateForJobApplication,
    getProfileCompletionSuggestions,
    REQUIRED_PROFILE_FIELDS,
    CRITICAL_FIELDS
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/api/auth.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getAuthToken": (()=>getAuthToken),
    "getRefreshToken": (()=>getRefreshToken),
    "login": (()=>login),
    "removeAuthToken": (()=>removeAuthToken),
    "setAuthToken": (()=>setAuthToken),
    "setRefreshToken": (()=>setRefreshToken),
    "signup": (()=>signup),
    "uploadResume": (()=>uploadResume)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/client.js [app-client] (ecmascript)");
;
function signup(data) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/api/auth/register/student/', data);
}
function login(data) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/api/auth/login/', data);
}
function uploadResume(file, accessToken) {
    const formData = new FormData();
    formData.append('resume', file);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch('/api/auth/profile/', formData, {
        headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'multipart/form-data'
        }
    });
}
const getAuthToken = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        return localStorage.getItem('access_token');
    }
    "TURBOPACK unreachable";
};
const setAuthToken = (token)=>{
    if ("TURBOPACK compile-time truthy", 1) {
        localStorage.setItem('access_token', token);
    }
};
const removeAuthToken = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
    }
};
const getRefreshToken = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        return localStorage.getItem('refresh_token');
    }
    "TURBOPACK unreachable";
};
const setRefreshToken = (token)=>{
    if ("TURBOPACK compile-time truthy", 1) {
        localStorage.setItem('refresh_token', token);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/api/students.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "studentsAPI": (()=>studentsAPI)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/auth.js [app-client] (ecmascript)");
;
;
// Set the base URL for all API requests
const API_BASE_URL = 'http://localhost:8000';
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json'
    }
});
// Add request interceptor to include auth token
api.interceptors.request.use((config)=>{
    const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Add response interceptor for error handling
api.interceptors.response.use((response)=>response, (error)=>{
    if (error.response?.status === 401) {
        // Token expired or invalid
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
    }
    return Promise.reject(error);
});
const studentsAPI = {
    // Get all students
    getStudents: async (params = {})=>{
        const response = await api.get('/api/accounts/students/', {
            params
        });
        return response.data;
    },
    // Get students with statistics
    getStudentsWithStats: async (params = {})=>{
        try {
            // First try to get students with built-in statistics
            const response = await api.get('/api/accounts/students/stats/', {
                params
            });
            return response.data;
        } catch (error) {
            // Fallback to regular students endpoint
            console.log('Stats endpoint not available, using regular endpoint');
            const response = await api.get('/api/accounts/students/', {
                params
            });
            // Calculate basic statistics from the response
            const students = response.data.data || response.data;
            if (Array.isArray(students)) {
                const stats = calculateStudentStats(students, params);
                return {
                    ...response.data,
                    statistics: stats
                };
            }
            return response.data;
        }
    },
    // Get single student
    getStudent: async (id)=>{
        const response = await api.get(`/api/accounts/students/${id}/`);
        return response.data;
    },
    // Update student
    updateStudent: async (id, data)=>{
        console.log('updateStudent called with:', {
            id,
            data
        });
        // Check authentication
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        console.log('Auth token available:', !!token);
        if (token) {
            console.log('Token preview:', token.substring(0, 20) + '...');
        }
        if (!token) {
            throw new Error('Authentication required to update student');
        }
        // Clean data to ensure proper format
        const cleanedData = {
            ...data
        };
        // Ensure numeric fields are properly formatted
        [
            'joining_year',
            'passout_year'
        ].forEach((field)=>{
            if (cleanedData[field] !== null && cleanedData[field] !== undefined) {
                const num = parseInt(cleanedData[field]);
                cleanedData[field] = isNaN(num) ? null : num;
            }
        });
        // Ensure string fields are properly formatted
        const stringFields = [
            'first_name',
            'last_name',
            'student_id',
            'contact_email',
            'phone',
            'branch',
            'gpa',
            'date_of_birth',
            'address',
            'city',
            'district',
            'state',
            'pincode',
            'country',
            'parent_contact',
            'education',
            'skills',
            'tenth_cgpa',
            'tenth_percentage',
            'tenth_board',
            'tenth_school',
            'tenth_year_of_passing',
            'tenth_location',
            'tenth_specialization',
            'twelfth_cgpa',
            'twelfth_percentage',
            'twelfth_board',
            'twelfth_school',
            'twelfth_year_of_passing',
            'twelfth_location',
            'twelfth_specialization'
        ];
        stringFields.forEach((field)=>{
            if (cleanedData[field] !== null && cleanedData[field] !== undefined) {
                cleanedData[field] = String(cleanedData[field]).trim();
            }
        });
        // Remove undefined values
        Object.keys(cleanedData).forEach((key)=>{
            if (cleanedData[key] === undefined) {
                delete cleanedData[key];
            }
        });
        console.log('Cleaned data being sent:', cleanedData);
        // Try the ViewSet endpoint first (more RESTful)
        try {
            console.log('Trying ViewSet endpoint:', `/api/accounts/profiles/${id}/`);
            const response = await api.patch(`/api/accounts/profiles/${id}/`, cleanedData);
            console.log('ViewSet endpoint success:', response.data);
            return response.data;
        } catch (error) {
            console.error('ViewSet endpoint failed:', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
                headers: error.response?.headers,
                config: error.config
            });
            // If ViewSet fails, try the fallback endpoint
            try {
                console.log('Trying fallback endpoint:', `/api/accounts/students/${id}/update/`);
                const response = await api.patch(`/api/accounts/students/${id}/update/`, cleanedData);
                console.log('Fallback endpoint success:', response.data);
                return response.data;
            } catch (updateError) {
                console.error('Failed to update student via both endpoints:', {
                    viewSetError: {
                        status: error.response?.status,
                        data: error.response?.data
                    },
                    updateViewError: {
                        status: updateError.response?.status,
                        data: updateError.response?.data
                    }
                });
                // Throw the more specific error
                const primaryError = updateError.response?.status === 400 ? updateError : error;
                throw primaryError;
            }
        }
    },
    // Get current user profile
    getProfile: async ()=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        return api.get('/api/auth/profile/', {
            headers: {
                Authorization: `Bearer ${token}`
            }
        }).then((response)=>response.data);
    },
    // Update profile information
    updateProfile: async (data)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        return api.patch('/api/auth/profile/', data, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        }).then((response)=>response.data);
    },
    // Upload profile image
    uploadProfileImage: async (file)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        const formData = new FormData();
        formData.append('image', file);
        return api.post('/api/accounts/profiles/me/upload_profile_image/', formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Upload resume using new Resume model
    uploadResume: async (file, name = null, isPrimary = false)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        const formData = new FormData();
        formData.append('file', file);
        if (name) {
            formData.append('name', name);
        }
        formData.append('is_primary', isPrimary);
        return api.post('/api/accounts/profiles/me/resumes/', formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Admin upload resume for specific student
    adminUploadResume: async (studentId, file, name = null, isPrimary = false)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        if (!token) {
            throw new Error('No authentication token found');
        }
        const formData = new FormData();
        formData.append('file', file);
        if (name) {
            formData.append('name', name);
        }
        formData.append('is_primary', isPrimary);
        return api.post(`/api/accounts/profiles/${studentId}/upload_resume/`, formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Admin get resumes for specific student
    adminGetResumes: async (studentId)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        if (!token) {
            throw new Error('No authentication token found');
        }
        return api.get(`/api/accounts/profiles/${studentId}/resumes/`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        }).then((response)=>response.data);
    },
    // Admin delete resume for specific student
    adminDeleteResume: async (studentId, resumeId)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Admin attempting to delete resume: ${resumeId} for student: ${studentId}`);
            const response = await api.delete(`/api/accounts/profiles/${studentId}/resumes/${resumeId}/`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            console.log('Admin DELETE resume successful:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error deleting resume:', error);
            throw error;
        }
    },
    // Admin upload certificate for specific student
    adminUploadCertificate: async (studentId, file, type)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        if (!token) {
            throw new Error('No authentication token found');
        }
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', type);
        return api.post(`/api/accounts/profiles/${studentId}/upload_certificate/`, formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Admin upload semester marksheet for specific student
    adminUploadSemesterMarksheet: async (studentId, file, semester, cgpa)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        if (!token) {
            throw new Error('No authentication token found');
        }
        const formData = new FormData();
        formData.append('marksheet_file', file);
        formData.append('semester', semester);
        formData.append('cgpa', cgpa);
        return api.post(`/api/accounts/profiles/${studentId}/upload_semester_marksheet/`, formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Legacy resume upload (for backward compatibility)
    uploadResumeToProfile: async (file)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        const formData = new FormData();
        formData.append('resume', file);
        return api.patch('/api/auth/profile/', formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Get all resumes for the student
    getResumes: async ()=>{
        try {
            const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
            if (!token) {
                console.log('No authentication token, returning empty array');
                return [];
            }
            // Try the new resume endpoint first
            const response = await api.get('/api/accounts/profiles/me/resumes/', {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            // Ensure we're getting a proper response
            if (!response.data) {
                return await studentsAPI.getResumesLegacy();
            }
            // Handle different response formats
            if (Array.isArray(response.data)) {
                return response.data;
            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
                return response.data.data;
            } else {
                console.log('Response data is not an array, trying fallback. Response:', response.data);
                try {
                    return await studentsAPI.getResumesLegacy();
                } catch (fallbackError) {
                    console.log('Fallback also failed, returning empty array');
                    return [];
                }
            }
        } catch (error) {
            console.log('Resume endpoint failed, using fallback method');
            try {
                return await studentsAPI.getResumesLegacy();
            } catch (fallbackError) {
                console.log('Fallback method also failed, returning empty array');
                return [];
            }
        }
    },
    // Legacy method to get resumes from profile
    getResumesLegacy: async ()=>{
        try {
            const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
            if (!token) {
                console.log('No auth token for legacy resume fetch');
                return [];
            }
            const profile = await studentsAPI.getProfile();
            if (profile?.resume || profile?.resume_url) {
                const resumeUrl = profile.resume_url || profile.resume;
                if (resumeUrl && resumeUrl.trim() !== '' && resumeUrl !== 'null' && resumeUrl !== 'undefined') {
                    const fileName = resumeUrl.split('/').pop() || 'Resume.pdf';
                    return [
                        {
                            id: profile.id || 1,
                            name: fileName,
                            file_url: resumeUrl,
                            uploaded_at: profile.updated_at || new Date().toISOString()
                        }
                    ];
                }
            }
            return [];
        } catch (error) {
            console.log('Legacy resume fetch error:', error.message);
            return [];
        }
    },
    // Delete a specific resume
    deleteResume: async (resumeId)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Attempting to delete resume with ID: ${resumeId}`);
            // Use the new Resume model endpoint
            const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            console.log('DELETE resume successful:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error deleting resume:', error);
            throw error;
        }
    },
    // Legacy delete function with fallback strategies
    deleteResumeLegacy: async (resumeId)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Attempting to delete resume with ID: ${resumeId}`);
            let success = false;
            // Attempt different deletion strategies
            const strategies = [
                // Strategy 1: Standard DELETE request
                async ()=>{
                    try {
                        const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('DELETE resume successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 1 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                },
                // Strategy 2: POST to remove endpoint
                async ()=>{
                    try {
                        const response = await api.post(`/api/accounts/profiles/me/resumes/${resumeId}/remove/`, {}, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('POST remove successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 2 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                },
                // Strategy 3: Patch profile with delete_resume field
                async ()=>{
                    try {
                        const response = await api.patch('/api/auth/profile/', {
                            delete_resume: resumeId
                        }, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('PATCH profile successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 3 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                },
                // Strategy 4: Reset all resumes (extreme fallback)
                async ()=>{
                    try {
                        const response = await api.patch('/api/auth/profile/', {
                            reset_resumes: true
                        }, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('Reset resumes successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 4 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                }
            ];
            // Try each strategy in sequence until one succeeds
            for (const strategy of strategies){
                const result = await strategy();
                if (result.success) {
                    success = true;
                    break;
                }
            }
            // Clear any locally cached data for this resume regardless of backend success
            if ("TURBOPACK compile-time truthy", 1) {
                // Clear any resume-related data from localStorage
                try {
                    const localStorageKeys = Object.keys(localStorage);
                    const resumeKeys = localStorageKeys.filter((key)=>key.includes('resume') || key.includes('file') || key.includes('document'));
                    if (resumeKeys.length > 0) {
                        console.log('Clearing resume-related localStorage items:', resumeKeys);
                        resumeKeys.forEach((key)=>localStorage.removeItem(key));
                    }
                    // Also try to clear specific keys that might be used for caching
                    localStorage.removeItem('resume_cache');
                    localStorage.removeItem('resume_list');
                    localStorage.removeItem('profile_cache');
                    localStorage.removeItem('resume_count');
                    localStorage.removeItem('last_resume_update');
                } catch (e) {
                    console.error('Error clearing localStorage:', e);
                }
            }
            return {
                success,
                message: success ? "Resume deleted successfully" : "Resume deleted locally but server sync failed"
            };
        } catch (error) {
            console.error('Resume deletion failed:', error.response?.status, error.message);
            // For UI purposes, return a success response even if backend fails
            // This allows the UI to remove the resume entry and maintain a good user experience
            return {
                success: true,
                synced: false,
                error: error.message,
                status: error.response?.status,
                message: "Resume removed from display (sync with server failed)"
            };
        }
    },
    // Upload certificate (10th or 12th)
    uploadCertificate: async (file, type)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        const formData = new FormData();
        formData.append('file', file); // Backend expects 'file', not 'certificate'
        formData.append('type', type);
        return api.post('/api/accounts/profiles/me/upload_certificate/', formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Get all certificates for the student
    getCertificates: async ()=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        if (!token) {
            throw new Error('Authentication required to fetch certificates');
        }
        try {
            const response = await api.get('/api/accounts/profiles/me/certificates/', {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            // Ensure we're getting a proper response
            if (!response.data) {
                console.error('Empty response when fetching certificates');
                return [];
            }
            // Handle different response formats
            if (Array.isArray(response.data)) {
                return response.data;
            } else if (response.data.data && Array.isArray(response.data.data)) {
                return response.data.data;
            } else {
                console.error('Unexpected certificate data format:', response.data);
                return [];
            }
        } catch (error) {
            console.error('Certificate fetch error:', error.response?.status, error.message);
            throw error;
        }
    },
    // Delete a specific certificate
    deleteCertificate: async (certificateType)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Attempting to delete certificate: ${certificateType}`);
            const response = await api.delete(`/api/accounts/profiles/me/delete_certificate/${certificateType}/`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            console.log('DELETE certificate successful:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error deleting certificate:', error);
            throw error;
        }
    },
    // Admin delete certificate for specific student
    adminDeleteCertificate: async (studentId, certificateType)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Admin attempting to delete certificate: ${certificateType} for student: ${studentId}`);
            const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_certificate/${certificateType}/`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            console.log('Admin DELETE certificate successful:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error deleting certificate:', error);
            throw error;
        }
    },
    // Delete a specific marksheet
    deleteMarksheet: async (semester)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Attempting to delete marksheet for semester: ${semester}`);
            const response = await api.delete(`/api/accounts/profiles/me/delete_marksheet/${semester}/`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            console.log('DELETE marksheet successful:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error deleting marksheet:', error);
            throw error;
        }
    },
    // Admin delete marksheet for specific student
    adminDeleteMarksheet: async (studentId, semester)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Admin attempting to delete marksheet for semester: ${semester} for student: ${studentId}`);
            const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_marksheet/${semester}/`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            console.log('Admin DELETE marksheet successful:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error deleting marksheet:', error);
            throw error;
        }
    },
    // Legacy delete function (keeping for backward compatibility)
    deleteCertificateLegacy: async (certificateId)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Attempting to delete certificate with ID: ${certificateId}`);
            let success = false;
            // Attempt different deletion strategies
            const strategies = [
                // Strategy 1: Standard DELETE request
                async ()=>{
                    try {
                        const response = await api.delete(`/api/accounts/profiles/me/certificates/${certificateId}/`, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('DELETE certificate successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 1 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                },
                // Strategy 2: POST to remove endpoint
                async ()=>{
                    try {
                        const response = await api.post(`/api/accounts/profiles/me/certificates/${certificateId}/remove/`, {}, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('POST remove successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 2 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                },
                // Strategy 3: Patch profile with delete_certificate field
                async ()=>{
                    try {
                        const response = await api.patch('/api/auth/profile/', {
                            delete_certificate: certificateId
                        }, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('PATCH profile successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 3 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                },
                // Strategy 4: Reset all certificates (extreme fallback)
                async ()=>{
                    try {
                        const response = await api.patch('/api/auth/profile/', {
                            reset_certificates: true
                        }, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('Reset certificates successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 4 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                }
            ];
            // Try each strategy in sequence until one succeeds
            for (const strategy of strategies){
                const result = await strategy();
                if (result.success) {
                    success = true;
                    break;
                }
            }
            // Clear any locally cached data for this certificate regardless of backend success
            if ("TURBOPACK compile-time truthy", 1) {
                // Clear any certificate-related data from localStorage
                try {
                    const localStorageKeys = Object.keys(localStorage);
                    const certificateKeys = localStorageKeys.filter((key)=>key.includes('certificate') || key.includes('document') || key.includes('cert'));
                    if (certificateKeys.length > 0) {
                        console.log('Clearing certificate-related localStorage items:', certificateKeys);
                        certificateKeys.forEach((key)=>localStorage.removeItem(key));
                    }
                    // Also try to clear specific keys that might be used for caching
                    localStorage.removeItem('certificate_cache');
                    localStorage.removeItem('certificate_list');
                    localStorage.removeItem('profile_cache');
                } catch (e) {
                    console.error('Error clearing localStorage:', e);
                }
            }
            return {
                success,
                message: success ? "Certificate deleted successfully" : "Certificate deleted locally but server sync failed"
            };
        } catch (error) {
            console.error('Certificate deletion failed:', error.response?.status, error.message);
            // For UI purposes, return a success response even if backend fails
            // This allows the UI to remove the certificate entry and maintain a good user experience
            return {
                success: true,
                synced: false,
                error: error.message,
                status: error.response?.status,
                message: "Certificate removed from display (sync with server failed)"
            };
        }
    },
    // Get semester marksheets
    getSemesterMarksheets: async ()=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        return api.get('/api/accounts/profiles/me/semester_marksheets/', {
            headers: {
                Authorization: `Bearer ${token}`
            }
        }).then((response)=>response.data);
    },
    // Upload semester marksheet
    uploadSemesterMarksheet: async (file, semester, cgpa)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        const formData = new FormData();
        formData.append('marksheet_file', file);
        formData.append('semester', semester);
        formData.append('cgpa', cgpa);
        return api.post('/api/accounts/profiles/me/upload_semester_marksheet/', formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Get current user's freeze status and restrictions
    getFreezeStatus: async ()=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        if (!token) {
            throw new Error('Authentication required to fetch freeze status');
        }
        try {
            const response = await api.get('/api/auth/profile/', {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            const profile = response.data;
            return {
                freeze_status: profile.freeze_status || 'none',
                freeze_reason: profile.freeze_reason,
                freeze_date: profile.freeze_date,
                min_salary_requirement: profile.min_salary_requirement,
                allowed_job_tiers: profile.allowed_job_tiers || [],
                allowed_job_types: profile.allowed_job_types || [],
                allowed_companies: profile.allowed_companies || []
            };
        } catch (error) {
            console.error('Freeze status fetch error:', error.response?.status, error.message);
            throw error;
        }
    },
    // Check if student can apply to a specific job
    canApplyToJob: async (jobId)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        if (!token) {
            throw new Error('Authentication required to check job application eligibility');
        }
        try {
            const response = await api.get(`/api/v1/college/default-college/jobs/${jobId}/can-apply/`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            return response.data;
        } catch (error) {
            console.error('Job application eligibility check error:', error.response?.status, error.message);
            if (error.response?.data) {
                console.error('Error details:', error.response.data);
            }
            throw error;
        }
    },
    // Get list of jobs the student has applied to
    getAppliedJobs: async ()=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        if (!token) {
            throw new Error('Authentication required to fetch applied jobs');
        }
        try {
            const response = await api.get('/api/v1/college/default-college/jobs/applied/', {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            return response.data;
        } catch (error) {
            console.error('Applied jobs fetch error:', error.response?.status, error.message);
            throw error;
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/jobpostings/[id]/apply/page.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>JobApplicationPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-client] (ecmascript) <export default as ArrowLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/map-pin.js [app-client] (ecmascript) <export default as MapPin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/building-2.js [app-client] (ecmascript) <export default as Building2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/dollar-sign.js [app-client] (ecmascript) <export default as DollarSign>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-client] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/briefcase.js [app-client] (ecmascript) <export default as Briefcase>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$award$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Award$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/award.js [app-client] (ecmascript) <export default as Award>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$jobs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/jobs.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/client.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/NotificationContext.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$profileValidation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/profileValidation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/students.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
// --- Reusable UI Components ---
// A reusable component for each section card in the form and review pages
const SectionCard = ({ title, onEdit, children, icon })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-200/80 mb-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between items-center mb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: [
                            icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "mr-2 text-lg",
                                children: icon
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 19,
                                columnNumber: 18
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-semibold text-gray-800",
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 20,
                                columnNumber: 9
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 18,
                        columnNumber: 7
                    }, this),
                    onEdit && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: onEdit,
                        className: "text-sm font-medium text-indigo-600 hover:text-indigo-800 transition-colors duration-200",
                        children: "Edit"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 23,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 17,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4",
                children: children
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 28,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 16,
        columnNumber: 3
    }, this);
_c = SectionCard;
// A reusable input field component
const InputField = ({ label, type = 'text', placeholder, name, value, onChange, isFullWidth = false, required = false })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: isFullWidth ? 'md:col-span-2' : '',
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: name,
                className: "block text-sm font-medium text-gray-700 mb-1.5",
                children: [
                    label,
                    " ",
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 38,
                        columnNumber: 28
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 37,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                type: type,
                name: name,
                id: name,
                placeholder: placeholder,
                value: value,
                onChange: onChange,
                required: required,
                className: "w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-shadow duration-200"
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 40,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 36,
        columnNumber: 3
    }, this);
_c1 = InputField;
// A reusable select field component
const SelectField = ({ label, name, value, onChange, children, required = false })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: name,
                className: "block text-sm font-medium text-gray-700 mb-1.5",
                children: [
                    label,
                    " ",
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 57,
                        columnNumber: 28
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 56,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                id: name,
                name: name,
                value: value,
                onChange: onChange,
                required: required,
                className: "w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-shadow duration-200",
                children: children
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 59,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 55,
        columnNumber: 3
    }, this);
_c2 = SelectField;
// A reusable textarea component
const TextareaField = ({ label, name, value, onChange, placeholder, rows = 6, tip, required = false })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "md:col-span-2",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: name,
                className: "block text-sm font-medium text-gray-700 mb-1.5",
                children: [
                    label,
                    " ",
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 76,
                        columnNumber: 28
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 75,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                id: name,
                name: name,
                rows: rows,
                value: value,
                onChange: onChange,
                required: required,
                className: "w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-shadow duration-200",
                placeholder: placeholder
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 78,
                columnNumber: 5
            }, this),
            tip && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-2 text-xs text-gray-500",
                children: tip
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 88,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 74,
        columnNumber: 3
    }, this);
_c3 = TextareaField;
// A reusable file input component with preview
const FileInput = ({ label, name, fileName, onChange, required = false })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "md:col-span-2",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                className: "block text-sm font-medium text-gray-700 mb-1.5",
                children: [
                    label,
                    " ",
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 96,
                        columnNumber: 28
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 95,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-1 flex items-center justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-indigo-400 transition-colors duration-200",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-1 text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "mx-auto h-12 w-12 text-gray-400",
                            stroke: "currentColor",
                            fill: "none",
                            viewBox: "0 0 48 48",
                            "aria-hidden": "true",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                d: "M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8",
                                strokeWidth: 2,
                                strokeLinecap: "round",
                                strokeLinejoin: "round"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 101,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 100,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex text-sm text-gray-600",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: name,
                                    className: "relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: "Upload a file"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 105,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            id: name,
                                            name: name,
                                            type: "file",
                                            className: "sr-only",
                                            onChange: onChange,
                                            required: required
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 106,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 104,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "pl-1",
                                    children: "or drag and drop"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 108,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 103,
                            columnNumber: 9
                        }, this),
                        fileName ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm font-semibold text-green-600",
                            children: fileName
                        }, void 0, false, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 111,
                            columnNumber: 11
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-xs text-gray-500",
                            children: "PDF, DOCX, PNG, JPG up to 10MB"
                        }, void 0, false, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 113,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 99,
                    columnNumber: 7
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 98,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 94,
        columnNumber: 3
    }, this);
_c4 = FileInput;
// --- Job Details Preview Component ---
const JobDetailsPreview = ({ job })=>{
    if (!job) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white rounded-xl shadow-sm border border-gray-200/80 p-6 mb-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-2xl font-bold text-gray-900 mb-2",
                        children: job.title
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 127,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center text-lg text-gray-600 mb-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__["Building2"], {
                                className: "w-5 h-5 mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 129,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: job.company_name
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 130,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 128,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center text-sm text-indigo-600 font-medium",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__["Briefcase"], {
                                className: "w-4 h-4 mr-1"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 133,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: job.job_type || 'FULL TIME'
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 134,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 132,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 126,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 pb-6 border-b border-gray-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__["DollarSign"], {
                                className: "w-5 h-5 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 141,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm font-medium text-gray-900",
                                        children: "Salary"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 143,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-gray-600",
                                        children: job.salary_min && job.salary_max ? `$${job.salary_min} - $${job.salary_max}` : "Competitive salary"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 144,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 142,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 140,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__["MapPin"], {
                                className: "w-5 h-5 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 153,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm font-medium text-gray-900",
                                        children: "Location"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 155,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-gray-600",
                                        children: job.location
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 156,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 154,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 152,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                className: "w-5 h-5 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 161,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm font-medium text-gray-900",
                                        children: "Deadline"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 163,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-gray-600",
                                        children: job.application_deadline ? new Date(job.application_deadline).toLocaleDateString() : "Not specified"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 164,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 162,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 160,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                className: "w-5 h-5 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 173,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm font-medium text-gray-900",
                                        children: "Duration"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 175,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-gray-600",
                                        children: job.duration || "Not specified"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 176,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 174,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 172,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 139,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 mb-3",
                        children: "Job Description"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 183,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-700 leading-relaxed",
                        children: job.description
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 184,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 182,
                columnNumber: 7
            }, this),
            job.requirements && job.requirements.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 mb-3",
                        children: "Requirements"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 190,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-wrap gap-2",
                        children: job.requirements.map((req, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "bg-gray-100 border border-gray-200 rounded-lg px-3 py-1 text-sm font-medium text-gray-700",
                                children: req
                            }, index, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 193,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 191,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 189,
                columnNumber: 9
            }, this),
            job.additional_fields && job.additional_fields.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 mb-3 flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                                className: "w-5 h-5 mr-2 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 205,
                                columnNumber: 13
                            }, this),
                            "Additional Information Required"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 204,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-gray-50 rounded-lg p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-600 mb-3",
                                children: "You'll need to provide the following information when applying:"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 209,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-2",
                                children: job.additional_fields.map((field, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-2 h-2 bg-indigo-600 rounded-full"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                lineNumber: 215,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-sm text-gray-700",
                                                children: [
                                                    field.label,
                                                    field.required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-red-500 ml-1",
                                                        children: "*"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                        lineNumber: 218,
                                                        columnNumber: 40
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                lineNumber: 216,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded",
                                                children: field.type
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                lineNumber: 220,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, index, true, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 214,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 212,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 208,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 203,
                columnNumber: 9
            }, this),
            job.interview_rounds && job.interview_rounds.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 mb-3 flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                className: "w-5 h-5 mr-2 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 234,
                                columnNumber: 13
                            }, this),
                            "Interview Process"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 233,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-3",
                        children: job.interview_rounds.map((round, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-8 h-8 bg-indigo-600 text-white rounded-full flex items-center justify-center text-sm font-semibold flex-shrink-0",
                                        children: index + 1
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 240,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "font-medium text-gray-900",
                                                children: round.name
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                lineNumber: 244,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-600",
                                                children: round.description
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                lineNumber: 245,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 243,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 239,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 237,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 232,
                columnNumber: 9
            }, this),
            (job.min_cgpa || job.allowed_branches || job.min_tenth_percentage || job.min_twelfth_percentage) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 mb-3 flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$award$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Award$3e$__["Award"], {
                                className: "w-5 h-5 mr-2 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 257,
                                columnNumber: 13
                            }, this),
                            "Eligibility Requirements"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 256,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-blue-50 border border-blue-200 rounded-lg p-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                            children: [
                                job.min_cgpa && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                            className: "w-4 h-4 text-blue-600"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 264,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm text-gray-700",
                                            children: [
                                                "Minimum CGPA: ",
                                                job.min_cgpa
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 265,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 263,
                                    columnNumber: 17
                                }, this),
                                job.allowed_branches && job.allowed_branches.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                            className: "w-4 h-4 text-blue-600"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 270,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm text-gray-700",
                                            children: [
                                                "Branches: ",
                                                job.allowed_branches.join(', ')
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 271,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 269,
                                    columnNumber: 17
                                }, this),
                                job.min_tenth_percentage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                            className: "w-4 h-4 text-blue-600"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 276,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm text-gray-700",
                                            children: [
                                                "10th Percentage: ",
                                                job.min_tenth_percentage,
                                                "%"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 277,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 275,
                                    columnNumber: 17
                                }, this),
                                job.min_twelfth_percentage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                            className: "w-4 h-4 text-blue-600"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 282,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm text-gray-700",
                                            children: [
                                                "12th Percentage: ",
                                                job.min_twelfth_percentage,
                                                "%"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 283,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 281,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 261,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 260,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 255,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-yellow-50 border border-yellow-200 rounded-lg p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-start gap-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                            className: "w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"
                        }, void 0, false, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 294,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "font-medium text-yellow-800 mb-1",
                                    children: "Application Tips"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 296,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                    className: "text-sm text-yellow-700 space-y-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "• Ensure your profile is complete with all required information"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 298,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "• Prepare a compelling cover letter highlighting relevant experience"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 299,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "• Have your resume and any required documents ready"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 300,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "• Review the job requirements and eligibility criteria carefully"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 301,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 297,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 295,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 293,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 292,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 125,
        columnNumber: 5
    }, this);
};
_c5 = JobDetailsPreview;
// --- Form View Component ---
const ApplicationForm = ({ job, formData, setFormData, setStep, canApply = true })=>{
    const handleChange = (e)=>{
        const { name, value, type, files } = e.target;
        if (type === 'file') {
            setFormData((prev)=>({
                    ...prev,
                    additional_fields: {
                        ...prev.additional_fields,
                        [name]: files[0]
                    }
                }));
        } else {
            setFormData((prev)=>({
                    ...prev,
                    [name]: value
                }));
        }
    };
    const handleAdditionalFieldChange = (fieldId, value)=>{
        setFormData((prev)=>({
                ...prev,
                additional_fields: {
                    ...prev.additional_fields,
                    [fieldId]: value
                }
            }));
    };
    const renderAdditionalField = (field)=>{
        const fieldId = `field_${field.id}`;
        const value = formData.additional_fields[fieldId] || '';
        switch(field.type){
            case 'text':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(InputField, {
                    label: field.label,
                    name: fieldId,
                    value: value,
                    onChange: (e)=>handleAdditionalFieldChange(fieldId, e.target.value),
                    placeholder: `Enter ${field.label.toLowerCase()}`,
                    required: field.required
                }, field.id, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 344,
                    columnNumber: 11
                }, this);
            case 'number':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(InputField, {
                    label: field.label,
                    type: "number",
                    name: fieldId,
                    value: value,
                    onChange: (e)=>handleAdditionalFieldChange(fieldId, e.target.value),
                    placeholder: `Enter ${field.label.toLowerCase()}`,
                    required: field.required
                }, field.id, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 357,
                    columnNumber: 11
                }, this);
            case 'file':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FileInput, {
                    label: field.label,
                    name: fieldId,
                    fileName: value instanceof File ? value.name : '',
                    onChange: (e)=>{
                        const file = e.target.files[0];
                        handleAdditionalFieldChange(fieldId, file);
                    },
                    required: field.required
                }, field.id, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 371,
                    columnNumber: 11
                }, this);
            case 'multiple_choice':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectField, {
                    label: field.label,
                    name: fieldId,
                    value: value,
                    onChange: (e)=>handleAdditionalFieldChange(fieldId, e.target.value),
                    required: field.required,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                            value: "",
                            children: [
                                "Select ",
                                field.label.toLowerCase()
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 394,
                            columnNumber: 13
                        }, this),
                        field.options?.map((option, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: option,
                                children: option
                            }, index, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 396,
                                columnNumber: 15
                            }, this))
                    ]
                }, field.id, true, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 386,
                    columnNumber: 11
                }, this);
            default:
                return null;
        }
    };
    const handleSubmit = (e)=>{
        e.preventDefault();
        setStep('review');
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
        onSubmit: handleSubmit,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SectionCard, {
                title: "📝 Cover Letter",
                icon: "📝",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(TextareaField, {
                    label: "Cover Letter",
                    name: "cover_letter",
                    value: formData.cover_letter,
                    onChange: handleChange,
                    placeholder: "Dear Hiring Manager, I am writing to express my interest in this position...",
                    tip: "Tip: Mention specific skills from the job requirements and explain how your experience aligns with the role.",
                    required: true
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 416,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 415,
                columnNumber: 7
            }, this),
            job.additional_fields && job.additional_fields.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SectionCard, {
                title: "📋 Additional Information",
                icon: "📋",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "md:col-span-2 space-y-4",
                    children: job.additional_fields.map((field)=>renderAdditionalField(field))
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 429,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 428,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-8 flex justify-end",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    type: "submit",
                    disabled: !canApply,
                    className: `w-full sm:w-auto px-6 py-3 font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ${canApply ? 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`,
                    children: canApply ? 'Review Application' : 'Application Restricted'
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 436,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 435,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 414,
        columnNumber: 5
    }, this);
};
_c6 = ApplicationForm;
// --- Review View Component ---
const ReviewApplication = ({ job, formData, setStep, onSubmit, isSubmitting, canApply = true })=>{
    // A reusable row for displaying a piece of data
    const DataRow = ({ label, value })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "py-3 sm:grid sm:grid-cols-3 sm:gap-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("dt", {
                    className: "text-sm font-medium text-gray-500",
                    children: label
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 458,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("dd", {
                    className: "mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words",
                    children: value || 'Not provided'
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 459,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
            lineNumber: 457,
            columnNumber: 5
        }, this);
    const TextData = ({ label, value })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "py-3",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("dt", {
                    className: "text-sm font-medium text-gray-500 mb-2",
                    children: label
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 465,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("dd", {
                    className: "text-sm text-gray-800 p-4 bg-gray-50 rounded-lg whitespace-pre-wrap",
                    children: value || 'Not provided'
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 466,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
            lineNumber: 464,
            columnNumber: 5
        }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SectionCard, {
                title: "📝 Cover Letter",
                onEdit: ()=>setStep('form'),
                icon: "📝",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "md:col-span-2",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(TextData, {
                        label: "Cover Letter",
                        value: formData.cover_letter
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 474,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 473,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 472,
                columnNumber: 7
            }, this),
            job.additional_fields && job.additional_fields.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SectionCard, {
                title: "📋 Additional Information",
                onEdit: ()=>setStep('form'),
                icon: "📋",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "md:col-span-2 divide-y divide-gray-200",
                    children: job.additional_fields.map((field)=>{
                        const fieldId = `field_${field.id}`;
                        const value = formData.additional_fields[fieldId];
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DataRow, {
                            label: field.label,
                            value: field.type === 'file' && value instanceof File ? value.name : value
                        }, field.id, false, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 485,
                            columnNumber: 17
                        }, this);
                    })
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 480,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 479,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-8 flex justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: ()=>setStep('form'),
                        className: "px-6 py-3 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200",
                        children: "Back to Edit"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 497,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: onSubmit,
                        disabled: isSubmitting || !canApply,
                        className: `px-8 py-3 font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 ${canApply ? 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`,
                        children: isSubmitting ? "Submitting..." : canApply ? "Submit Application" : "Application Restricted"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 504,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 496,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 471,
        columnNumber: 5
    }, this);
};
_c7 = ReviewApplication;
function JobApplicationPage() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const params = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"])();
    const jobId = params.id;
    const { showApplicationSubmissionError, showSuccess, handleApiError, showProfileIncompleteModal } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotification"])();
    const [job, setJob] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [step, setStep] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('form'); // 'form' or 'review'
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [userProfile, setUserProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [profileValidation, setProfileValidation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [freezeStatus, setFreezeStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [canApply, setCanApply] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [freezeRestrictions, setFreezeRestrictions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // Form data state
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        cover_letter: '',
        additional_fields: {}
    });
    // Fetch job details
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "JobApplicationPage.useEffect": ()=>{
            const fetchJobDetails = {
                "JobApplicationPage.useEffect.fetchJobDetails": async ()=>{
                    try {
                        setLoading(true);
                        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/college/default-college/jobs/${jobId}/`);
                        setJob(response.data);
                        setError(null);
                    } catch (err) {
                        console.error('Failed to fetch job details:', err);
                        handleApiError(err, 'loading job details');
                        setError('Failed to load job details. Please try again.');
                    } finally{
                        setLoading(false);
                    }
                }
            }["JobApplicationPage.useEffect.fetchJobDetails"];
            if (jobId) {
                fetchJobDetails();
                fetchUserProfile();
                checkJobApplicationEligibility();
            }
        }
    }["JobApplicationPage.useEffect"], [
        jobId
    ]);
    // Fetch user profile for validation
    const fetchUserProfile = async ()=>{
        try {
            const token = localStorage.getItem('access_token');
            if (!token) return;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/api/auth/profile/');
            const profile = response.data;
            setUserProfile(profile);
            // Validate profile for job application
            const jobRequirements = job ? {
                minCgpa: job.min_cgpa,
                allowedBranches: job.allowed_branches,
                minTenthPercentage: job.min_tenth_percentage,
                minTwelfthPercentage: job.min_twelfth_percentage
            } : {};
            const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$profileValidation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateForJobApplication"])(profile, jobRequirements);
            setProfileValidation(validation);
        } catch (err) {
            console.error('Failed to fetch user profile:', err);
        }
    };
    // Check freeze status and job application eligibility
    const checkJobApplicationEligibility = async ()=>{
        if (!jobId) return;
        try {
            const eligibilityResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].canApplyToJob(jobId);
            setCanApply(eligibilityResponse.can_apply);
            if (!eligibilityResponse.can_apply) {
                setFreezeStatus({
                    status: eligibilityResponse.freeze_status,
                    reason: eligibilityResponse.reason,
                    freeze_reason: eligibilityResponse.freeze_reason,
                    restrictions: eligibilityResponse.restrictions || []
                });
                setFreezeRestrictions(eligibilityResponse.restrictions || []);
            }
        } catch (err) {
            console.error('Failed to check job application eligibility:', err);
            // Handle specific error cases
            if (err.response?.status === 400 && err.response?.data?.reason) {
                // User doesn't have student profile or other validation error
                setCanApply(false);
                setFreezeStatus({
                    status: 'error',
                    reason: err.response.data.reason,
                    freeze_reason: '',
                    restrictions: []
                });
            } else if (err.response?.status === 500) {
                // Server error - show error message but allow application
                setCanApply(true);
                console.error('Server error checking eligibility:', err.response?.data);
            } else {
                // Other errors - allow application but log the error
                setCanApply(true);
            }
        }
    };
    // Re-validate when job data changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "JobApplicationPage.useEffect": ()=>{
            if (userProfile && job) {
                const jobRequirements = {
                    minCgpa: job.min_cgpa,
                    allowedBranches: job.allowed_branches,
                    minTenthPercentage: job.min_tenth_percentage,
                    minTwelfthPercentage: job.min_twelfth_percentage
                };
                const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$profileValidation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateForJobApplication"])(userProfile, jobRequirements);
                setProfileValidation(validation);
            }
        }
    }["JobApplicationPage.useEffect"], [
        userProfile,
        job
    ]);
    const handleSubmit = async ()=>{
        // Check profile validation first
        if (profileValidation && !profileValidation.canApply) {
            const missingFields = profileValidation.missing.filter((field)=>[
                    'Resume',
                    'First Name',
                    'Last Name',
                    'Email',
                    'Student ID/Roll Number'
                ].includes(field));
            showProfileIncompleteModal(missingFields);
            return;
        }
        // Check job-specific eligibility
        if (profileValidation?.jobSpecific && !profileValidation.jobSpecific.isEligible) {
            showApplicationSubmissionError({
                response: {
                    data: {
                        eligibility: profileValidation.jobSpecific.errors
                    }
                }
            });
            return;
        }
        if (!formData.cover_letter.trim()) {
            showApplicationSubmissionError({
                response: {
                    data: {
                        cover_letter: [
                            'Cover letter is required.'
                        ]
                    }
                }
            });
            return;
        }
        setIsSubmitting(true);
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$jobs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applyToJob"])(jobId, formData.cover_letter, formData.additional_fields);
            // Success - show success notification and redirect
            showSuccess('Application Submitted!', 'Your job application has been submitted successfully. Good luck!');
            // Redirect after a short delay to let user see the success message
            setTimeout(()=>{
                router.push('/jobpostings');
            }, 2000);
        } catch (err) {
            console.error('Failed to submit application:', err);
            showApplicationSubmissionError(err);
        } finally{
            setIsSubmitting(false);
        }
    };
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gray-50 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-600 mx-auto"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 708,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-4 text-gray-600",
                        children: "Loading job details..."
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 709,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 707,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
            lineNumber: 706,
            columnNumber: 7
        }, this);
    }
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gray-50 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-red-600 mb-4",
                        children: error
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 719,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>router.push('/jobpostings'),
                        className: "px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700",
                        children: "Back to Jobs"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 720,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 718,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
            lineNumber: 717,
            columnNumber: 7
        }, this);
    }
    if (!job) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gray-50 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600 mb-4",
                        children: "Job not found"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 735,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>router.push('/jobpostings'),
                        className: "px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700",
                        children: "Back to Jobs"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 736,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 734,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
            lineNumber: 733,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-gray-50 font-sans",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container mx-auto max-w-4xl p-4 sm:p-6 lg:p-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                    className: "mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center mb-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>router.push('/jobpostings'),
                                className: "flex items-center text-gray-600 hover:text-gray-900 mr-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__["ArrowLeft"], {
                                        className: "w-5 h-5 mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 758,
                                        columnNumber: 15
                                    }, this),
                                    "Back to Jobs"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 754,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 753,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-white rounded-xl shadow-sm border border-gray-200/80 p-6 mb-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                    className: "text-2xl sm:text-3xl font-bold text-gray-900 mb-2",
                                                    children: job.title
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 766,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center text-lg text-gray-600 mb-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__["Building2"], {
                                                            className: "w-5 h-5 mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                            lineNumber: 768,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: job.company_name
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                            lineNumber: 769,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 767,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 765,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2 mt-4 lg:mt-0",
                                            children: [
                                                1,
                                                2,
                                                3
                                            ].map((stepNumber)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: `w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${step === 'form' && stepNumber === 1 || step === 'review' && stepNumber <= 2 ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-600'}`,
                                                            children: stepNumber
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                            lineNumber: 777,
                                                            columnNumber: 21
                                                        }, this),
                                                        stepNumber < 3 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: `w-8 h-0.5 mx-2 ${step === 'review' && stepNumber < 2 ? 'bg-indigo-600' : 'bg-gray-200'}`
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                            lineNumber: 785,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, stepNumber, true, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 776,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 774,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 764,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-1 md:grid-cols-3 gap-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center text-sm text-gray-600",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__["MapPin"], {
                                                    className: "w-4 h-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 796,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: job.location
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 797,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 795,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center text-sm text-gray-600",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__["DollarSign"], {
                                                    className: "w-4 h-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 800,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: job.salary_min && job.salary_max ? `$${job.salary_min} - $${job.salary_max}` : "Competitive salary"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 801,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 799,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center text-sm text-gray-600",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                                    className: "w-4 h-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 808,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: [
                                                        "Deadline: ",
                                                        job.application_deadline ? new Date(job.application_deadline).toLocaleDateString() : "Not specified"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 809,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 807,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 794,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 763,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 752,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(JobDetailsPreview, {
                    job: job
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 820,
                    columnNumber: 9
                }, this),
                !canApply && freezeStatus && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-red-50 border border-red-200 rounded-lg p-4 mb-6",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-start",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                                className: "w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 826,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-red-800 font-semibold mb-2",
                                        children: freezeStatus.status === 'complete' ? 'Account Completely Frozen' : 'Account Partially Restricted'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 828,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-red-700 mb-3",
                                        children: freezeStatus.reason
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 831,
                                        columnNumber: 17
                                    }, this),
                                    freezeStatus.freeze_reason && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-red-100 rounded-md p-3 mb-3",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-red-800 font-medium text-sm",
                                            children: [
                                                "Admin Reason: ",
                                                freezeStatus.freeze_reason
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 836,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 835,
                                        columnNumber: 19
                                    }, this),
                                    freezeRestrictions.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-red-700 font-medium text-sm mb-2",
                                                children: "Specific restrictions for this job:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                lineNumber: 843,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                className: "list-disc list-inside text-red-600 text-sm space-y-1",
                                                children: freezeRestrictions.map((restriction, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                        children: restriction
                                                    }, index, false, {
                                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                        lineNumber: 846,
                                                        columnNumber: 25
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                lineNumber: 844,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 842,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 827,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 825,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 824,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                    children: step === 'form' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ApplicationForm, {
                        job: job,
                        formData: formData,
                        setFormData: setFormData,
                        setStep: setStep,
                        canApply: canApply
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 859,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ReviewApplication, {
                        job: job,
                        formData: formData,
                        setStep: setStep,
                        onSubmit: handleSubmit,
                        isSubmitting: isSubmitting,
                        canApply: canApply
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 867,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 857,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
            lineNumber: 749,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 748,
        columnNumber: 5
    }, this);
}
_s(JobApplicationPage, "Z0nDJSIBmwhZMtvMg776UQ9+Qtk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotification"]
    ];
});
_c8 = JobApplicationPage;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;
__turbopack_context__.k.register(_c, "SectionCard");
__turbopack_context__.k.register(_c1, "InputField");
__turbopack_context__.k.register(_c2, "SelectField");
__turbopack_context__.k.register(_c3, "TextareaField");
__turbopack_context__.k.register(_c4, "FileInput");
__turbopack_context__.k.register(_c5, "JobDetailsPreview");
__turbopack_context__.k.register(_c6, "ApplicationForm");
__turbopack_context__.k.register(_c7, "ReviewApplication");
__turbopack_context__.k.register(_c8, "JobApplicationPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_c6072187._.js.map