{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_adbadb87._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_9cb4b359.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4L1N1ApV6JxRkywf2zSy/shy6TmjdExKn7foFPP8JTY=", "__NEXT_PREVIEW_MODE_ID": "fd6adcce19cb4cddd72bce87fa7e059e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9a7ba52d180e28d036d387460228fba63441d87a921d4ce620b40bd869702274", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "63ef44050211456e950b7afebbe81314e06ba9b2c71c034fe76389e3b3a93da0"}}}, "instrumentation": null, "functions": {}}